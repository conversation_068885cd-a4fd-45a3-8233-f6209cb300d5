/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return e.d(r,{a:r}),r},d:(t,r)=>{for(var i in r)e.o(r,i)&&!e.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:r[i]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{initialize:()=>Er,initializeEditor:()=>Sr,reinitializeEditor:()=>Ar,store:()=>nt});var r={};e.r(r),e.d(r,{closeModal:()=>$,disableComplementaryArea:()=>V,enableComplementaryArea:()=>O,openModal:()=>U,pinItem:()=>D,setDefaultComplementaryArea:()=>M,setFeatureDefaults:()=>H,setFeatureValue:()=>z,toggleFeature:()=>G,unpinItem:()=>F});var i={};e.r(i),e.d(i,{getActiveComplementaryArea:()=>Y,isComplementaryAreaLoading:()=>Z,isFeatureActive:()=>q,isItemPinned:()=>K,isModalActive:()=>J});var s={};e.r(s),e.d(s,{closeGeneralSidebar:()=>Pe,moveBlockToWidgetArea:()=>Me,persistStubPost:()=>Ee,saveEditedWidgetAreas:()=>Ae,saveWidgetArea:()=>Ce,saveWidgetAreas:()=>Ie,setIsInserterOpened:()=>Re,setIsListViewOpened:()=>We,setIsWidgetAreaOpen:()=>Le,setWidgetAreasOpenState:()=>Te,setWidgetIdForClientId:()=>Be});var o={};e.r(o),e.d(o,{getWidgetAreas:()=>Oe,getWidgets:()=>Ve});var n={};e.r(n),e.d(n,{__experimentalGetInsertionPoint:()=>Je,canInsertBlockInWidgetArea:()=>Qe,getEditedWidgetAreas:()=>$e,getIsWidgetAreaOpen:()=>Ke,getParentWidgetAreaBlock:()=>Ue,getReferenceWidgetBlocks:()=>Ye,getWidget:()=>Ge,getWidgetAreaForWidgetId:()=>He,getWidgetAreas:()=>ze,getWidgets:()=>Fe,isInserterOpened:()=>qe,isListViewOpened:()=>Xe,isSavingWidgetAreas:()=>Ze});var a={};e.r(a),e.d(a,{getInserterSidebarToggleRef:()=>tt,getListViewToggleRef:()=>et});var c={};e.r(c),e.d(c,{metadata:()=>pt,name:()=>ht,settings:()=>mt});const d=window.wp.blocks,l=window.wp.data,u=window.wp.deprecated;var g=e.n(u);const p=window.wp.element,h=window.wp.blockLibrary,m=window.wp.coreData,w=window.wp.widgets,_=window.wp.preferences,b=window.wp.apiFetch;var f=e.n(b);const x=(0,l.combineReducers)({blockInserterPanel:function(e=!1,t){switch(t.type){case"SET_IS_LIST_VIEW_OPENED":return!t.isOpen&&e;case"SET_IS_INSERTER_OPENED":return t.value}return e},inserterSidebarToggleRef:function(e={current:null}){return e},listViewPanel:function(e=!1,t){switch(t.type){case"SET_IS_INSERTER_OPENED":return!t.value&&e;case"SET_IS_LIST_VIEW_OPENED":return t.isOpen}return e},listViewToggleRef:function(e={current:null}){return e},widgetAreasOpenState:function(e={},t){const{type:r}=t;switch(r){case"SET_WIDGET_AREAS_OPEN_STATE":return t.widgetAreasOpenState;case"SET_IS_WIDGET_AREA_OPEN":{const{clientId:r,isOpen:i}=t;return{...e,[r]:i}}default:return e}}}),y=window.wp.i18n,v=window.wp.notices;function k(e){var t,r,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(r=k(e[t]))&&(i&&(i+=" "),i+=r)}else for(r in e)e[r]&&(i&&(i+=" "),i+=r);return i}const j=function(){for(var e,t,r=0,i="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=k(e))&&(i&&(i+=" "),i+=t);return i},S=window.wp.components,E=window.wp.primitives,A=window.ReactJSXRuntime,I=(0,A.jsx)(E.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})}),C=(0,A.jsx)(E.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"})}),N=(0,A.jsx)(E.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{fillRule:"evenodd",d:"M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z",clipRule:"evenodd"})}),B=window.wp.viewport,T=window.wp.compose,L=window.wp.plugins,R=(0,A.jsx)(E.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})});function W(e){return["core/edit-post","core/edit-site"].includes(e)?(g()(`${e} interface scope`,{alternative:"core interface scope",hint:"core/edit-post and core/edit-site are merging.",version:"6.6"}),"core"):e}function P(e,t){return"core"===e&&"edit-site/template"===t?(g()("edit-site/template sidebar",{alternative:"edit-post/document",version:"6.6"}),"edit-post/document"):"core"===e&&"edit-site/block-inspector"===t?(g()("edit-site/block-inspector sidebar",{alternative:"edit-post/block",version:"6.6"}),"edit-post/block"):t}const M=(e,t)=>({type:"SET_DEFAULT_COMPLEMENTARY_AREA",scope:e=W(e),area:t=P(e,t)}),O=(e,t)=>({registry:r,dispatch:i})=>{if(!t)return;e=W(e),t=P(e,t);r.select(_.store).get(e,"isComplementaryAreaVisible")||r.dispatch(_.store).set(e,"isComplementaryAreaVisible",!0),i({type:"ENABLE_COMPLEMENTARY_AREA",scope:e,area:t})},V=e=>({registry:t})=>{e=W(e);t.select(_.store).get(e,"isComplementaryAreaVisible")&&t.dispatch(_.store).set(e,"isComplementaryAreaVisible",!1)},D=(e,t)=>({registry:r})=>{if(!t)return;e=W(e),t=P(e,t);const i=r.select(_.store).get(e,"pinnedItems");!0!==i?.[t]&&r.dispatch(_.store).set(e,"pinnedItems",{...i,[t]:!0})},F=(e,t)=>({registry:r})=>{if(!t)return;e=W(e),t=P(e,t);const i=r.select(_.store).get(e,"pinnedItems");r.dispatch(_.store).set(e,"pinnedItems",{...i,[t]:!1})};function G(e,t){return function({registry:r}){g()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),r.dispatch(_.store).toggle(e,t)}}function z(e,t,r){return function({registry:i}){g()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),i.dispatch(_.store).set(e,t,!!r)}}function H(e,t){return function({registry:r}){g()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),r.dispatch(_.store).setDefaults(e,t)}}function U(e){return{type:"OPEN_MODAL",name:e}}function $(){return{type:"CLOSE_MODAL"}}const Y=(0,l.createRegistrySelector)((e=>(t,r)=>{r=W(r);const i=e(_.store).get(r,"isComplementaryAreaVisible");if(void 0!==i)return!1===i?null:t?.complementaryAreas?.[r]})),Z=(0,l.createRegistrySelector)((e=>(t,r)=>{r=W(r);const i=e(_.store).get(r,"isComplementaryAreaVisible"),s=t?.complementaryAreas?.[r];return i&&void 0===s})),K=(0,l.createRegistrySelector)((e=>(t,r,i)=>{var s;i=P(r=W(r),i);const o=e(_.store).get(r,"pinnedItems");return null===(s=o?.[i])||void 0===s||s})),q=(0,l.createRegistrySelector)((e=>(t,r,i)=>(g()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(_.store).get(r,i))));function J(e,t){return e.activeModal===t}const Q=(0,l.combineReducers)({complementaryAreas:function(e={},t){switch(t.type){case"SET_DEFAULT_COMPLEMENTARY_AREA":{const{scope:r,area:i}=t;return e[r]?e:{...e,[r]:i}}case"ENABLE_COMPLEMENTARY_AREA":{const{scope:r,area:i}=t;return{...e,[r]:i}}}return e},activeModal:function(e=null,t){switch(t.type){case"OPEN_MODAL":return t.name;case"CLOSE_MODAL":return null}return e}}),X=(0,l.createReduxStore)("core/interface",{reducer:Q,actions:r,selectors:i});function ee({as:e=S.Button,scope:t,identifier:r,icon:i,selectedIcon:s,name:o,shortcut:n,...a}){const c=e,d=(0,L.usePluginContext)(),u=i||d.icon,g=r||`${d.name}/${o}`,p=(0,l.useSelect)((e=>e(X).getActiveComplementaryArea(t)===g),[g,t]),{enableComplementaryArea:h,disableComplementaryArea:m}=(0,l.useDispatch)(X);return(0,A.jsx)(c,{icon:s&&p?s:u,"aria-controls":g.replace("/",":"),"aria-checked":(w=a.role,["checkbox","option","radio","switch","menuitemcheckbox","menuitemradio","treeitem"].includes(w)?p:void 0),onClick:()=>{p?m(t):h(t,g)},shortcut:n,...a});var w}(0,l.register)(X);const te=({children:e,className:t,toggleButtonProps:r})=>{const i=(0,A.jsx)(ee,{icon:R,...r});return(0,A.jsxs)("div",{className:j("components-panel__header","interface-complementary-area-header",t),tabIndex:-1,children:[e,i]})},re=()=>{};function ie({name:e,as:t=S.Button,onClick:r,...i}){return(0,A.jsx)(S.Fill,{name:e,children:({onClick:e})=>(0,A.jsx)(t,{onClick:r||e?(...t)=>{(r||re)(...t),(e||re)(...t)}:void 0,...i})})}ie.Slot=function({name:e,as:t=S.MenuGroup,fillProps:r={},bubblesVirtually:i,...s}){return(0,A.jsx)(S.Slot,{name:e,bubblesVirtually:i,fillProps:r,children:e=>{if(!p.Children.toArray(e).length)return null;const r=[];p.Children.forEach(e,(({props:{__unstableExplicitMenuItem:e,__unstableTarget:t}})=>{t&&e&&r.push(t)}));const i=p.Children.map(e,(e=>!e.props.__unstableExplicitMenuItem&&r.includes(e.props.__unstableTarget)?null:e));return(0,A.jsx)(t,{...s,children:i})}})};const se=ie,oe=({__unstableExplicitMenuItem:e,__unstableTarget:t,...r})=>(0,A.jsx)(S.MenuItem,{...r});function ne({scope:e,target:t,__unstableExplicitMenuItem:r,...i}){return(0,A.jsx)(ee,{as:i=>(0,A.jsx)(se,{__unstableExplicitMenuItem:r,__unstableTarget:`${e}/${t}`,as:oe,name:`${e}/plugin-more-menu`,...i}),role:"menuitemcheckbox",selectedIcon:I,name:t,scope:e,...i})}function ae({scope:e,...t}){return(0,A.jsx)(S.Fill,{name:`PinnedItems/${e}`,...t})}ae.Slot=function({scope:e,className:t,...r}){return(0,A.jsx)(S.Slot,{name:`PinnedItems/${e}`,...r,children:e=>e?.length>0&&(0,A.jsx)("div",{className:j(t,"interface-pinned-items"),children:e})})};const ce=ae;const de={open:{width:280},closed:{width:0},mobileOpen:{width:"100vw"}};function le({activeArea:e,isActive:t,scope:r,children:i,className:s,id:o}){const n=(0,T.useReducedMotion)(),a=(0,T.useViewportMatch)("medium","<"),c=(0,T.usePrevious)(e),d=(0,T.usePrevious)(t),[,l]=(0,p.useState)({});(0,p.useEffect)((()=>{l({})}),[t]);const u={type:"tween",duration:n||a||c&&e&&e!==c?0:.3,ease:[.6,0,.4,1]};return(0,A.jsx)(S.Fill,{name:`ComplementaryArea/${r}`,children:(0,A.jsx)(S.__unstableAnimatePresence,{initial:!1,children:(d||t)&&(0,A.jsx)(S.__unstableMotion.div,{variants:de,initial:"closed",animate:a?"mobileOpen":"open",exit:"closed",transition:u,className:"interface-complementary-area__fill",children:(0,A.jsx)("div",{id:o,className:s,style:{width:a?"100vw":280},children:i})})})})}function ue({children:e,className:t,closeLabel:r=(0,y.__)("Close plugin"),identifier:i,header:s,headerClassName:o,icon:n,isPinnable:a=!0,panelClassName:c,scope:d,name:u,title:g,toggleShortcut:h,isActiveByDefault:m}){const w=(0,L.usePluginContext)(),b=n||w.icon,f=i||`${w.name}/${u}`,[x,v]=(0,p.useState)(!1),{isLoading:k,isActive:E,isPinned:R,activeArea:W,isSmall:P,isLarge:M,showIconLabels:O}=(0,l.useSelect)((e=>{const{getActiveComplementaryArea:t,isComplementaryAreaLoading:r,isItemPinned:i}=e(X),{get:s}=e(_.store),o=t(d);return{isLoading:r(d),isActive:o===f,isPinned:i(d,f),activeArea:o,isSmall:e(B.store).isViewportMatch("< medium"),isLarge:e(B.store).isViewportMatch("large"),showIconLabels:s("core","showIconLabels")}}),[f,d]),V=(0,T.useViewportMatch)("medium","<");!function(e,t,r,i,s){const o=(0,p.useRef)(!1),n=(0,p.useRef)(!1),{enableComplementaryArea:a,disableComplementaryArea:c}=(0,l.useDispatch)(X);(0,p.useEffect)((()=>{i&&s&&!o.current?(c(e),n.current=!0):n.current&&!s&&o.current?(n.current=!1,a(e,t)):n.current&&r&&r!==t&&(n.current=!1),s!==o.current&&(o.current=s)}),[i,s,e,t,r,c,a])}(d,f,W,E,P);const{enableComplementaryArea:D,disableComplementaryArea:F,pinItem:G,unpinItem:z}=(0,l.useDispatch)(X);if((0,p.useEffect)((()=>{m&&void 0===W&&!P?D(d,f):void 0===W&&P&&F(d,f),v(!0)}),[W,m,d,f,P,D,F]),x)return(0,A.jsxs)(A.Fragment,{children:[a&&(0,A.jsx)(ce,{scope:d,children:R&&(0,A.jsx)(ee,{scope:d,identifier:f,isPressed:E&&(!O||M),"aria-expanded":E,"aria-disabled":k,label:g,icon:O?I:b,showTooltip:!O,variant:O?"tertiary":void 0,size:"compact",shortcut:h})}),u&&a&&(0,A.jsx)(ne,{target:u,scope:d,icon:b,children:g}),(0,A.jsxs)(le,{activeArea:W,isActive:E,className:j("interface-complementary-area",t),scope:d,id:f.replace("/",":"),children:[(0,A.jsx)(te,{className:o,closeLabel:r,onClose:()=>F(d),toggleButtonProps:{label:r,size:"compact",shortcut:h,scope:d,identifier:f},children:s||(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("h2",{className:"interface-complementary-area-header__title",children:g}),a&&!V&&(0,A.jsx)(S.Button,{className:"interface-complementary-area__pin-unpin-item",icon:R?C:N,label:R?(0,y.__)("Unpin from toolbar"):(0,y.__)("Pin to toolbar"),onClick:()=>(R?z:G)(d,f),isPressed:R,"aria-expanded":R,size:"compact"})]})}),(0,A.jsx)(S.Panel,{className:c,children:e})]})]})}ue.Slot=function({scope:e,...t}){return(0,A.jsx)(S.Slot,{name:`ComplementaryArea/${e}`,...t})};const ge=ue,pe=(0,p.forwardRef)((({children:e,className:t,ariaLabel:r,as:i="div",...s},o)=>(0,A.jsx)(i,{ref:o,className:j("interface-navigable-region",t),"aria-label":r,role:"region",tabIndex:"-1",...s,children:e})));pe.displayName="NavigableRegion";const he=pe,me={type:"tween",duration:.25,ease:[.6,0,.4,1]};const we={hidden:{opacity:1,marginTop:-60},visible:{opacity:1,marginTop:0},distractionFreeHover:{opacity:1,marginTop:0,transition:{...me,delay:.2,delayChildren:.2}},distractionFreeHidden:{opacity:0,marginTop:-60},distractionFreeDisabled:{opacity:0,marginTop:0,transition:{...me,delay:.8,delayChildren:.8}}};const _e=(0,p.forwardRef)((function({isDistractionFree:e,footer:t,header:r,editorNotices:i,sidebar:s,secondarySidebar:o,content:n,actions:a,labels:c,className:d},l){const[u,g]=(0,T.useResizeObserver)(),h=(0,T.useViewportMatch)("medium","<"),m={type:"tween",duration:(0,T.useReducedMotion)()?0:.25,ease:[.6,0,.4,1]};!function(e){(0,p.useEffect)((()=>{const t=document&&document.querySelector(`html:not(.${e})`);if(t)return t.classList.toggle(e),()=>{t.classList.toggle(e)}}),[e])}("interface-interface-skeleton__html-container");const w={...{header:(0,y._x)("Header","header landmark area"),body:(0,y.__)("Content"),secondarySidebar:(0,y.__)("Block Library"),sidebar:(0,y._x)("Settings","settings landmark area"),actions:(0,y.__)("Publish"),footer:(0,y.__)("Footer")},...c};return(0,A.jsxs)("div",{ref:l,className:j(d,"interface-interface-skeleton",!!t&&"has-footer"),children:[(0,A.jsxs)("div",{className:"interface-interface-skeleton__editor",children:[(0,A.jsx)(S.__unstableAnimatePresence,{initial:!1,children:!!r&&(0,A.jsx)(he,{as:S.__unstableMotion.div,className:"interface-interface-skeleton__header","aria-label":w.header,initial:e&&!h?"distractionFreeHidden":"hidden",whileHover:e&&!h?"distractionFreeHover":"visible",animate:e&&!h?"distractionFreeDisabled":"visible",exit:e&&!h?"distractionFreeHidden":"hidden",variants:we,transition:m,children:r})}),e&&(0,A.jsx)("div",{className:"interface-interface-skeleton__header",children:i}),(0,A.jsxs)("div",{className:"interface-interface-skeleton__body",children:[(0,A.jsx)(S.__unstableAnimatePresence,{initial:!1,children:!!o&&(0,A.jsx)(he,{className:"interface-interface-skeleton__secondary-sidebar",ariaLabel:w.secondarySidebar,as:S.__unstableMotion.div,initial:"closed",animate:"open",exit:"closed",variants:{open:{width:g.width},closed:{width:0}},transition:m,children:(0,A.jsxs)(S.__unstableMotion.div,{style:{position:"absolute",width:h?"100vw":"fit-content",height:"100%",left:0},variants:{open:{x:0},closed:{x:"-100%"}},transition:m,children:[u,o]})})}),(0,A.jsx)(he,{className:"interface-interface-skeleton__content",ariaLabel:w.body,children:n}),!!s&&(0,A.jsx)(he,{className:"interface-interface-skeleton__sidebar",ariaLabel:w.sidebar,children:s}),!!a&&(0,A.jsx)(he,{className:"interface-interface-skeleton__actions",ariaLabel:w.actions,children:a})]})]}),!!t&&(0,A.jsx)(he,{className:"interface-interface-skeleton__footer",ariaLabel:w.footer,children:t})]})})),be=window.wp.blockEditor;function fe(e){if("block"===e.id_base){const t=(0,d.parse)(e.instance.raw.content,{__unstableSkipAutop:!0});return t.length?(0,w.addWidgetIdToBlock)(t[0],e.id):(0,w.addWidgetIdToBlock)((0,d.createBlock)("core/paragraph",{},[]),e.id)}let t;return t=e._embedded.about[0].is_multi?{idBase:e.id_base,instance:e.instance}:{id:e.id},(0,w.addWidgetIdToBlock)((0,d.createBlock)("core/legacy-widget",t,[]),e.id)}function xe(e,t={}){let r;var i,s,o;"core/legacy-widget"===e.name&&(e.attributes.id||e.attributes.instance)?r={...t,id:null!==(i=e.attributes.id)&&void 0!==i?i:t.id,id_base:null!==(s=e.attributes.idBase)&&void 0!==s?s:t.id_base,instance:null!==(o=e.attributes.instance)&&void 0!==o?o:t.instance}:r={...t,id_base:"block",instance:{raw:{content:(0,d.serialize)(e)}}};return delete r.rendered,delete r.rendered_form,r}const ye="root",ve="sidebar",ke="postType",je=e=>`widget-area-${e}`;const Se="core/edit-widgets",Ee=(e,t)=>({registry:r})=>{const i=((e,t)=>({id:e,slug:e,status:"draft",type:"page",blocks:t,meta:{widgetAreaId:e}}))(e,t);return r.dispatch(m.store).receiveEntityRecords(ye,ke,i,{id:i.id},!1),i},Ae=()=>async({select:e,dispatch:t,registry:r})=>{const i=e.getEditedWidgetAreas();if(i?.length)try{await t.saveWidgetAreas(i),r.dispatch(v.store).createSuccessNotice((0,y.__)("Widgets saved."),{type:"snackbar"})}catch(e){r.dispatch(v.store).createErrorNotice((0,y.sprintf)((0,y.__)("There was an error. %s"),e.message),{type:"snackbar"})}},Ie=e=>async({dispatch:t,registry:r})=>{try{for(const r of e)await t.saveWidgetArea(r.id)}finally{await r.dispatch(m.store).finishResolution("getEntityRecord",ye,ve,{per_page:-1})}},Ce=e=>async({dispatch:t,select:r,registry:i})=>{const s=r.getWidgets(),o=i.select(m.store).getEditedEntityRecord(ye,ke,je(e)),n=Object.values(s).filter((({sidebar:t})=>t===e)),a=[],c=o.blocks.filter((e=>{const{id:t}=e.attributes;if("core/legacy-widget"===e.name&&t){if(a.includes(t))return!1;a.push(t)}return!0})),d=[];for(const e of n){r.getWidgetAreaForWidgetId(e.id)||d.push(e)}const l=[],u=[],g=[];for(let t=0;t<c.length;t++){const r=c[t],o=(0,w.getWidgetIdFromBlock)(r),n=s[o],a=xe(r,n);if(g.push(o),n){i.dispatch(m.store).editEntityRecord("root","widget",o,{...a,sidebar:e},{undoIgnore:!0});if(!i.select(m.store).hasEditsForEntityRecord("root","widget",o))continue;u.push((({saveEditedEntityRecord:e})=>e("root","widget",o)))}else u.push((({saveEntityRecord:t})=>t("root","widget",{...a,sidebar:e})));l.push({block:r,position:t,clientId:r.clientId})}for(const e of d)u.push((({deleteEntityRecord:t})=>t("root","widget",e.id,{force:!0})));const p=(await i.dispatch(m.store).__experimentalBatch(u)).filter((e=>!e.hasOwnProperty("deleted"))),h=[];for(let e=0;e<p.length;e++){const t=p[e],{block:r,position:s}=l[e];o.blocks[s].attributes.__internalWidgetId=t.id;i.select(m.store).getLastEntitySaveError("root","widget",t.id)&&h.push(r.attributes?.name||r?.name),g[s]||(g[s]=t.id)}if(h.length)throw new Error((0,y.sprintf)((0,y.__)("Could not save the following widgets: %s."),h.join(", ")));i.dispatch(m.store).editEntityRecord(ye,ve,e,{widgets:g},{undoIgnore:!0}),t(Ne(e)),i.dispatch(m.store).receiveEntityRecords(ye,ke,o,void 0)},Ne=e=>({registry:t})=>{t.dispatch(m.store).saveEditedEntityRecord(ye,ve,e,{throwOnError:!0})};function Be(e,t){return{type:"SET_WIDGET_ID_FOR_CLIENT_ID",clientId:e,widgetId:t}}function Te(e){return{type:"SET_WIDGET_AREAS_OPEN_STATE",widgetAreasOpenState:e}}function Le(e,t){return{type:"SET_IS_WIDGET_AREA_OPEN",clientId:e,isOpen:t}}function Re(e){return{type:"SET_IS_INSERTER_OPENED",value:e}}function We(e){return{type:"SET_IS_LIST_VIEW_OPENED",isOpen:e}}const Pe=()=>({registry:e})=>{e.dispatch(X).disableComplementaryArea(Se)},Me=(e,t)=>async({dispatch:r,select:i,registry:s})=>{const o=s.select(be.store).getBlockRootClientId(e),n=s.select(be.store).getBlocks().find((({attributes:e})=>e.id===t)).clientId,a=s.select(be.store).getBlockOrder(n).length;i.getIsWidgetAreaOpen(n)||r.setIsWidgetAreaOpen(n,!0),s.dispatch(be.store).moveBlocksToPosition([e],o,n,a)},Oe=()=>async({dispatch:e,registry:t})=>{const r={per_page:-1},i=[],s=(await t.resolveSelect(m.store).getEntityRecords(ye,ve,r)).sort(((e,t)=>"wp_inactive_widgets"===e.id?1:"wp_inactive_widgets"===t.id?-1:0));for(const t of s)i.push((0,d.createBlock)("core/widget-area",{id:t.id,name:t.name})),t.widgets.length||e(Ee(je(t.id),[]));const o={};i.forEach(((e,t)=>{o[e.clientId]=0===t})),e(Te(o)),e(Ee("widget-areas",i))},Ve=()=>async({dispatch:e,registry:t})=>{const r={per_page:-1,_embed:"about"},i=await t.resolveSelect(m.store).getEntityRecords("root","widget",r),s={};for(const e of i){const t=fe(e);s[e.sidebar]=s[e.sidebar]||[],s[e.sidebar].push(t)}for(const t in s)s.hasOwnProperty(t)&&e(Ee(je(t),s[t]))},De={rootClientId:void 0,insertionIndex:void 0},Fe=(0,l.createRegistrySelector)((e=>(0,l.createSelector)((()=>{var t;const r=e(m.store).getEntityRecords("root","widget",{per_page:-1,_embed:"about"});return null!==(t=r?.reduce(((e,t)=>({...e,[t.id]:t})),{}))&&void 0!==t?t:{}}),(()=>[e(m.store).getEntityRecords("root","widget",{per_page:-1,_embed:"about"})])))),Ge=(0,l.createRegistrySelector)((e=>(t,r)=>e(Se).getWidgets()[r])),ze=(0,l.createRegistrySelector)((e=>()=>{const t={per_page:-1};return e(m.store).getEntityRecords(ye,ve,t)})),He=(0,l.createRegistrySelector)((e=>(t,r)=>e(Se).getWidgetAreas().find((t=>e(m.store).getEditedEntityRecord(ye,ke,je(t.id)).blocks.map((e=>(0,w.getWidgetIdFromBlock)(e))).includes(r))))),Ue=(0,l.createRegistrySelector)((e=>(t,r)=>{const{getBlock:i,getBlockName:s,getBlockParents:o}=e(be.store);return i(o(r).find((e=>"core/widget-area"===s(e))))})),$e=(0,l.createRegistrySelector)((e=>(t,r)=>{let i=e(Se).getWidgetAreas();return i?(r&&(i=i.filter((({id:e})=>r.includes(e)))),i.filter((({id:t})=>e(m.store).hasEditsForEntityRecord(ye,ke,je(t)))).map((({id:t})=>e(m.store).getEditedEntityRecord(ye,ve,t)))):[]})),Ye=(0,l.createRegistrySelector)((e=>(t,r=null)=>{const i=[],s=e(Se).getWidgetAreas();for(const t of s){const s=e(m.store).getEditedEntityRecord(ye,ke,je(t.id));for(const e of s.blocks)"core/legacy-widget"!==e.name||r&&e.attributes?.referenceWidgetName!==r||i.push(e)}return i})),Ze=(0,l.createRegistrySelector)((e=>()=>{const t=e(Se).getWidgetAreas()?.map((({id:e})=>e));if(!t)return!1;for(const r of t){if(e(m.store).isSavingEntityRecord(ye,ve,r))return!0}const r=[...Object.keys(e(Se).getWidgets()),void 0];for(const t of r){if(e(m.store).isSavingEntityRecord("root","widget",t))return!0}return!1})),Ke=(e,t)=>{const{widgetAreasOpenState:r}=e;return!!r[t]};function qe(e){return!!e.blockInserterPanel}function Je(e){return"boolean"==typeof e.blockInserterPanel?De:e.blockInserterPanel}const Qe=(0,l.createRegistrySelector)((e=>(t,r)=>{const i=e(be.store).getBlocks(),[s]=i;return e(be.store).canInsertBlockType(r,s.clientId)}));function Xe(e){return e.listViewPanel}function et(e){return e.listViewToggleRef}function tt(e){return e.inserterSidebarToggleRef}const rt=window.wp.privateApis,{lock:it,unlock:st}=(0,rt.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/edit-widgets"),ot={reducer:x,selectors:n,resolvers:o,actions:s},nt=(0,l.createReduxStore)(Se,ot);(0,l.register)(nt),f().use((function(e,t){return 0===e.path?.indexOf("/wp/v2/types/widget-area")?Promise.resolve({}):t(e)})),st(nt).registerPrivateSelectors(a);const at=window.wp.hooks,ct=(0,T.createHigherOrderComponent)((e=>t=>{const{clientId:r,name:i}=t,{widgetAreas:s,currentWidgetAreaId:o,canInsertBlockInWidgetArea:n}=(0,l.useSelect)((e=>{if("core/widget-area"===i)return{};const t=e(nt),s=t.getParentWidgetAreaBlock(r);return{widgetAreas:t.getWidgetAreas(),currentWidgetAreaId:s?.attributes?.id,canInsertBlockInWidgetArea:t.canInsertBlockInWidgetArea(i)}}),[r,i]),{moveBlockToWidgetArea:a}=(0,l.useDispatch)(nt),c="core/widget-area"!==i&&s?.length>1&&n;return(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)(e,{...t},"edit"),c&&(0,A.jsx)(be.BlockControls,{children:(0,A.jsx)(w.MoveToWidgetArea,{widgetAreas:s,currentWidgetAreaId:o,onSelect:e=>{a(t.clientId,e)}})})]})}),"withMoveToWidgetAreaToolbarItem");(0,at.addFilter)("editor.BlockEdit","core/edit-widgets/block-edit",ct);const dt=window.wp.mediaUtils;(0,at.addFilter)("editor.MediaUpload","core/edit-widgets/replace-media-upload",(()=>dt.MediaUpload));const lt=e=>{const[t,r]=(0,p.useState)(!1);return(0,p.useEffect)((()=>{const{ownerDocument:t}=e.current;function i(e){o(e)}function s(){r(!1)}function o(t){e.current.contains(t.target)?r(!0):r(!1)}return t.addEventListener("dragstart",i),t.addEventListener("dragend",s),t.addEventListener("dragenter",o),()=>{t.removeEventListener("dragstart",i),t.removeEventListener("dragend",s),t.removeEventListener("dragenter",o)}}),[]),t};function ut({id:e}){const[t,r,i]=(0,m.useEntityBlockEditor)("root","postType"),s=(0,p.useRef)(),o=lt(s),n=(0,be.useInnerBlocksProps)({ref:s},{value:t,onInput:r,onChange:i,templateLock:!1,renderAppender:be.InnerBlocks.ButtonBlockAppender});return(0,A.jsx)("div",{"data-widget-area-id":e,className:j("wp-block-widget-area__inner-blocks block-editor-inner-blocks editor-styles-wrapper",{"wp-block-widget-area__highlight-drop-zone":o}),children:(0,A.jsx)("div",{...n})})}const gt=e=>{const[t,r]=(0,p.useState)(!1);return(0,p.useEffect)((()=>{const{ownerDocument:t}=e.current;function i(){r(!0)}function s(){r(!1)}return t.addEventListener("dragstart",i),t.addEventListener("dragend",s),()=>{t.removeEventListener("dragstart",i),t.removeEventListener("dragend",s)}}),[]),t},pt={$schema:"https://schemas.wp.org/trunk/block.json",name:"core/widget-area",title:"Widget Area",category:"widgets",attributes:{id:{type:"string"},name:{type:"string"}},supports:{html:!1,inserter:!1,customClassName:!1,reusable:!1,__experimentalToolbar:!1,__experimentalParentSelector:!1,__experimentalDisableBlockOverlay:!0},editorStyle:"wp-block-widget-area-editor",style:"wp-block-widget-area"},{name:ht}=pt,mt={title:(0,y.__)("Widget Area"),description:(0,y.__)("A widget area container."),__experimentalLabel:({name:e})=>e,edit:function({clientId:e,className:t,attributes:{id:r,name:i}}){const s=(0,l.useSelect)((t=>t(nt).getIsWidgetAreaOpen(e)),[e]),{setIsWidgetAreaOpen:o}=(0,l.useDispatch)(nt),n=(0,p.useRef)(),a=(0,p.useCallback)((t=>o(e,t)),[e]),c=gt(n),d=lt(n),[u,g]=(0,p.useState)(!1);return(0,p.useEffect)((()=>{c?d&&!s?(a(!0),g(!0)):!d&&s&&u&&a(!1):g(!1)}),[s,c,d,u]),(0,A.jsx)(S.Panel,{className:t,ref:n,children:(0,A.jsx)(S.PanelBody,{title:i,opened:s,onToggle:()=>{o(e,!s)},scrollAfterOpen:!c,children:({opened:e})=>(0,A.jsx)(S.__unstableDisclosureContent,{className:"wp-block-widget-area__panel-body-content",visible:e,children:(0,A.jsx)(m.EntityProvider,{kind:"root",type:"postType",id:`widget-area-${r}`,children:(0,A.jsx)(ut,{id:r})})})})})}};function wt({text:e,children:t}){const r=(0,T.useCopyToClipboard)(e);return(0,A.jsx)(S.Button,{__next40pxDefaultSize:!0,variant:"secondary",ref:r,children:t})}function _t({message:e,error:t}){const r=[(0,A.jsx)(wt,{text:t.stack,children:(0,y.__)("Copy Error")},"copy-error")];return(0,A.jsx)(be.Warning,{className:"edit-widgets-error-boundary",actions:r,children:e})}class bt extends p.Component{constructor(){super(...arguments),this.state={error:null}}componentDidCatch(e){(0,at.doAction)("editor.ErrorBoundary.errorLogged",e)}static getDerivedStateFromError(e){return{error:e}}render(){return this.state.error?(0,A.jsx)(_t,{message:(0,y.__)("The editor has encountered an unexpected error."),error:this.state.error}):this.props.children}}const ft=window.wp.patterns,xt=window.wp.keyboardShortcuts,yt=window.wp.keycodes;function vt(){const{redo:e,undo:t}=(0,l.useDispatch)(m.store),{saveEditedWidgetAreas:r}=(0,l.useDispatch)(nt);return(0,xt.useShortcut)("core/edit-widgets/undo",(e=>{t(),e.preventDefault()})),(0,xt.useShortcut)("core/edit-widgets/redo",(t=>{e(),t.preventDefault()})),(0,xt.useShortcut)("core/edit-widgets/save",(e=>{e.preventDefault(),r()})),null}vt.Register=function(){const{registerShortcut:e}=(0,l.useDispatch)(xt.store);return(0,p.useEffect)((()=>{e({name:"core/edit-widgets/undo",category:"global",description:(0,y.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"core/edit-widgets/redo",category:"global",description:(0,y.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"},aliases:(0,yt.isAppleOS)()?[]:[{modifier:"primary",character:"y"}]}),e({name:"core/edit-widgets/save",category:"global",description:(0,y.__)("Save your changes."),keyCombination:{modifier:"primary",character:"s"}}),e({name:"core/edit-widgets/keyboard-shortcuts",category:"main",description:(0,y.__)("Display these keyboard shortcuts."),keyCombination:{modifier:"access",character:"h"}}),e({name:"core/edit-widgets/next-region",category:"global",description:(0,y.__)("Navigate to the next part of the editor."),keyCombination:{modifier:"ctrl",character:"`"},aliases:[{modifier:"access",character:"n"}]}),e({name:"core/edit-widgets/previous-region",category:"global",description:(0,y.__)("Navigate to the previous part of the editor."),keyCombination:{modifier:"ctrlShift",character:"`"},aliases:[{modifier:"access",character:"p"},{modifier:"ctrlShift",character:"~"}]})}),[e]),null};const kt=vt,jt=()=>(0,l.useSelect)((e=>{const{getBlockSelectionEnd:t,getBlockName:r}=e(be.store),i=t();if("core/widget-area"===r(i))return i;const{getParentWidgetAreaBlock:s}=e(nt),o=s(i),n=o?.clientId;if(n)return n;const{getEntityRecord:a}=e(m.store),c=a(ye,ke,"widget-areas");return c?.blocks[0]?.clientId}),[]),{ExperimentalBlockEditorProvider:St}=st(be.privateApis),{PatternsMenuItems:Et}=st(ft.privateApis),{BlockKeyboardShortcuts:At}=st(h.privateApis),It=[];function Ct({blockEditorSettings:e,children:t,...r}){const i=(0,T.useViewportMatch)("medium"),{hasUploadPermissions:s,reusableBlocks:o,isFixedToolbarActive:n,keepCaretInsideBlock:a,pageOnFront:c,pageForPosts:d}=(0,l.useSelect)((e=>{var t;const{canUser:r,getEntityRecord:i,getEntityRecords:s}=e(m.store),o=r("read",{kind:"root",name:"site"})?i("root","site"):void 0;return{hasUploadPermissions:null===(t=r("create",{kind:"root",name:"media"}))||void 0===t||t,reusableBlocks:It,isFixedToolbarActive:!!e(_.store).get("core/edit-widgets","fixedToolbar"),keepCaretInsideBlock:!!e(_.store).get("core/edit-widgets","keepCaretInsideBlock"),pageOnFront:o?.page_on_front,pageForPosts:o?.page_for_posts}}),[]),{setIsInserterOpened:u}=(0,l.useDispatch)(nt),g=(0,p.useMemo)((()=>{let t;return s&&(t=({onError:t,...r})=>{(0,dt.uploadMedia)({wpAllowedMimeTypes:e.allowedMimeTypes,onError:({message:e})=>t(e),...r})}),{...e,__experimentalReusableBlocks:o,hasFixedToolbar:n||!i,keepCaretInsideBlock:a,mediaUpload:t,templateLock:"all",__experimentalSetIsInserterOpened:u,pageOnFront:c,pageForPosts:d,editorTool:"edit"}}),[s,e,n,i,a,o,u,c,d]),h=jt(),[w,b,f]=(0,m.useEntityBlockEditor)(ye,ke,{id:"widget-areas"});return(0,A.jsxs)(S.SlotFillProvider,{children:[(0,A.jsx)(kt.Register,{}),(0,A.jsx)(At,{}),(0,A.jsxs)(St,{value:w,onInput:b,onChange:f,settings:g,useSubRegistry:!1,...r,children:[t,(0,A.jsx)(Et,{rootClientId:h})]})]})}const Nt=(0,A.jsx)(E.SVG,{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM8.5 18.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h2.5v13zm10-.5c0 .3-.2.5-.5.5h-8v-13h8c.3 0 .5.2.5.5v12z"})}),Bt=(0,A.jsx)(E.SVG,{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-4 14.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h8v13zm4.5-.5c0 .3-.2.5-.5.5h-2.5v-13H18c.3 0 .5.2.5.5v12z"})}),Tt=(0,A.jsx)(E.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{d:"M19 8h-1V6h-5v2h-2V6H6v2H5c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2zm.5 10c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-8c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v8z"})}),Lt=window.wp.url,Rt=window.wp.dom;function Wt({selectedWidgetAreaId:e}){const t=(0,l.useSelect)((e=>e(nt).getWidgetAreas()),[]),r=(0,p.useMemo)((()=>e&&t?.find((t=>t.id===e))),[e,t]);let i;return i=r?"wp_inactive_widgets"===e?(0,y.__)("Blocks in this Widget Area will not be displayed in your site."):r.description:(0,y.__)("Widget Areas are global parts in your site’s layout that can accept blocks. These vary by theme, but are typically parts like your Sidebar or Footer."),(0,A.jsx)("div",{className:"edit-widgets-widget-areas",children:(0,A.jsxs)("div",{className:"edit-widgets-widget-areas__top-container",children:[(0,A.jsx)(be.BlockIcon,{icon:Tt}),(0,A.jsxs)("div",{children:[(0,A.jsx)("p",{dangerouslySetInnerHTML:{__html:(0,Rt.safeHTML)(i)}}),0===t?.length&&(0,A.jsx)("p",{children:(0,y.__)("Your theme does not contain any Widget Areas.")}),!r&&(0,A.jsx)(S.Button,{__next40pxDefaultSize:!0,href:(0,Lt.addQueryArgs)("customize.php",{"autofocus[panel]":"widgets",return:window.location.pathname}),variant:"tertiary",children:(0,y.__)("Manage with live preview")})]})]})})}const Pt=p.Platform.select({web:!0,native:!1}),Mt="edit-widgets/block-inspector",Ot="edit-widgets/block-areas",{Tabs:Vt}=st(S.privateApis);function Dt({selectedWidgetAreaBlock:e}){return(0,A.jsxs)(Vt.TabList,{children:[(0,A.jsx)(Vt.Tab,{tabId:Ot,children:e?e.attributes.name:(0,y.__)("Widget Areas")}),(0,A.jsx)(Vt.Tab,{tabId:Mt,children:(0,y.__)("Block")})]})}function Ft({hasSelectedNonAreaBlock:e,currentArea:t,isGeneralSidebarOpen:r,selectedWidgetAreaBlock:i}){const{enableComplementaryArea:s}=(0,l.useDispatch)(X);(0,p.useEffect)((()=>{e&&t===Ot&&r&&s("core/edit-widgets",Mt),!e&&t===Mt&&r&&s("core/edit-widgets",Ot)}),[e,s]);const o=(0,p.useContext)(Vt.Context);return(0,A.jsx)(ge,{className:"edit-widgets-sidebar",header:(0,A.jsx)(Vt.Context.Provider,{value:o,children:(0,A.jsx)(Dt,{selectedWidgetAreaBlock:i})}),headerClassName:"edit-widgets-sidebar__panel-tabs",title:(0,y.__)("Settings"),closeLabel:(0,y.__)("Close Settings"),scope:"core/edit-widgets",identifier:t,icon:(0,y.isRTL)()?Nt:Bt,isActiveByDefault:Pt,children:(0,A.jsxs)(Vt.Context.Provider,{value:o,children:[(0,A.jsx)(Vt.TabPanel,{tabId:Ot,focusable:!1,children:(0,A.jsx)(Wt,{selectedWidgetAreaId:i?.attributes.id})}),(0,A.jsx)(Vt.TabPanel,{tabId:Mt,focusable:!1,children:e?(0,A.jsx)(be.BlockInspector,{}):(0,A.jsx)("span",{className:"block-editor-block-inspector__no-blocks",children:(0,y.__)("No block selected.")})})]})})}function Gt(){const{currentArea:e,hasSelectedNonAreaBlock:t,isGeneralSidebarOpen:r,selectedWidgetAreaBlock:i}=(0,l.useSelect)((e=>{const{getSelectedBlock:t,getBlock:r,getBlockParentsByBlockName:i}=e(be.store),{getActiveComplementaryArea:s}=e(X),o=t(),n=s(nt.name);let a,c=n;return c||(c=o?Mt:Ot),o&&(a="core/widget-area"===o.name?o:r(i(o.clientId,"core/widget-area")[0])),{currentArea:c,hasSelectedNonAreaBlock:!(!o||"core/widget-area"===o.name),isGeneralSidebarOpen:!!n,selectedWidgetAreaBlock:a}}),[]),{enableComplementaryArea:s}=(0,l.useDispatch)(X),o=(0,p.useCallback)((e=>{e&&s(nt.name,e)}),[s]);return(0,A.jsx)(Vt,{selectedTabId:r?e:null,onSelect:o,selectOnMove:!1,children:(0,A.jsx)(Ft,{hasSelectedNonAreaBlock:t,currentArea:e,isGeneralSidebarOpen:r,selectedWidgetAreaBlock:i})})}const zt=(0,A.jsx)(E.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"})}),Ht=(0,A.jsx)(E.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,A.jsx)(E.Path,{d:"M3 6h11v1.5H3V6Zm3.5 5.5h11V13h-11v-1.5ZM21 17H10v1.5h11V17Z"})}),Ut=(0,A.jsx)(E.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"})}),$t=(0,A.jsx)(E.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"})});const Yt=(0,p.forwardRef)((function(e,t){const r=(0,l.useSelect)((e=>e(m.store).hasUndo()),[]),{undo:i}=(0,l.useDispatch)(m.store);return(0,A.jsx)(S.Button,{...e,ref:t,icon:(0,y.isRTL)()?$t:Ut,label:(0,y.__)("Undo"),shortcut:yt.displayShortcut.primary("z"),"aria-disabled":!r,onClick:r?i:void 0,size:"compact"})}));const Zt=(0,p.forwardRef)((function(e,t){const r=(0,yt.isAppleOS)()?yt.displayShortcut.primaryShift("z"):yt.displayShortcut.primary("y"),i=(0,l.useSelect)((e=>e(m.store).hasRedo()),[]),{redo:s}=(0,l.useDispatch)(m.store);return(0,A.jsx)(S.Button,{...e,ref:t,icon:(0,y.isRTL)()?Ut:$t,label:(0,y.__)("Redo"),shortcut:r,"aria-disabled":!i,onClick:i?s:void 0,size:"compact"})}));const Kt=function(){const e=(0,T.useViewportMatch)("medium"),{isInserterOpen:t,isListViewOpen:r,inserterSidebarToggleRef:i,listViewToggleRef:s}=(0,l.useSelect)((e=>{const{isInserterOpened:t,getInserterSidebarToggleRef:r,isListViewOpened:i,getListViewToggleRef:s}=st(e(nt));return{isInserterOpen:t(),isListViewOpen:i(),inserterSidebarToggleRef:r(),listViewToggleRef:s()}}),[]),{setIsInserterOpened:o,setIsListViewOpened:n}=(0,l.useDispatch)(nt),a=(0,p.useCallback)((()=>n(!r)),[n,r]),c=(0,p.useCallback)((()=>o(!t)),[o,t]);return(0,A.jsxs)(be.NavigableToolbar,{className:"edit-widgets-header-toolbar","aria-label":(0,y.__)("Document tools"),variant:"unstyled",children:[(0,A.jsx)(S.ToolbarItem,{ref:i,as:S.Button,className:"edit-widgets-header-toolbar__inserter-toggle",variant:"primary",isPressed:t,onMouseDown:e=>{e.preventDefault()},onClick:c,icon:zt,label:(0,y._x)("Block Inserter","Generic label for block inserter button"),size:"compact"}),e&&(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)(S.ToolbarItem,{as:Yt}),(0,A.jsx)(S.ToolbarItem,{as:Zt}),(0,A.jsx)(S.ToolbarItem,{as:S.Button,className:"edit-widgets-header-toolbar__list-view-toggle",icon:Ht,isPressed:r,label:(0,y.__)("List View"),onClick:a,ref:s,size:"compact"})]})]})};const qt=function(){const{hasEditedWidgetAreaIds:e,isSaving:t}=(0,l.useSelect)((e=>{const{getEditedWidgetAreas:t,isSavingWidgetAreas:r}=e(nt);return{hasEditedWidgetAreaIds:t()?.length>0,isSaving:r()}}),[]),{saveEditedWidgetAreas:r}=(0,l.useDispatch)(nt),i=t||!e;return(0,A.jsx)(S.Button,{variant:"primary",isBusy:t,"aria-disabled":i,onClick:i?void 0:r,size:"compact",children:t?(0,y.__)("Saving…"):(0,y.__)("Update")})},Jt=(0,A.jsx)(E.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"})}),Qt=(0,A.jsx)(E.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,A.jsx)(E.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})}),Xt=[{keyCombination:{modifier:"primary",character:"b"},description:(0,y.__)("Make the selected text bold.")},{keyCombination:{modifier:"primary",character:"i"},description:(0,y.__)("Make the selected text italic.")},{keyCombination:{modifier:"primary",character:"k"},description:(0,y.__)("Convert the selected text into a link.")},{keyCombination:{modifier:"primaryShift",character:"k"},description:(0,y.__)("Remove a link.")},{keyCombination:{character:"[["},description:(0,y.__)("Insert a link to a post or page.")},{keyCombination:{modifier:"primary",character:"u"},description:(0,y.__)("Underline the selected text.")},{keyCombination:{modifier:"access",character:"d"},description:(0,y.__)("Strikethrough the selected text.")},{keyCombination:{modifier:"access",character:"x"},description:(0,y.__)("Make the selected text inline code.")},{keyCombination:{modifier:"access",character:"0"},aliases:[{modifier:"access",character:"7"}],description:(0,y.__)("Convert the current heading to a paragraph.")},{keyCombination:{modifier:"access",character:"1-6"},description:(0,y.__)("Convert the current paragraph or heading to a heading of level 1 to 6.")},{keyCombination:{modifier:"primaryShift",character:"SPACE"},description:(0,y.__)("Add non breaking space.")}];function er({keyCombination:e,forceAriaLabel:t}){const r=e.modifier?yt.displayShortcutList[e.modifier](e.character):e.character,i=e.modifier?yt.shortcutAriaLabel[e.modifier](e.character):e.character,s=Array.isArray(r)?r:[r];return(0,A.jsx)("kbd",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-key-combination","aria-label":t||i,children:s.map(((e,t)=>"+"===e?(0,A.jsx)(p.Fragment,{children:e},t):(0,A.jsx)("kbd",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-key",children:e},t)))})}const tr=function({description:e,keyCombination:t,aliases:r=[],ariaLabel:i}){return(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("div",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-description",children:e}),(0,A.jsxs)("div",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-term",children:[(0,A.jsx)(er,{keyCombination:t,forceAriaLabel:i}),r.map(((e,t)=>(0,A.jsx)(er,{keyCombination:e,forceAriaLabel:i},t)))]})]})};const rr=function({name:e}){const{keyCombination:t,description:r,aliases:i}=(0,l.useSelect)((t=>{const{getShortcutKeyCombination:r,getShortcutDescription:i,getShortcutAliases:s}=t(xt.store);return{keyCombination:r(e),aliases:s(e),description:i(e)}}),[e]);return t?(0,A.jsx)(tr,{keyCombination:t,description:r,aliases:i}):null},ir=({shortcuts:e})=>(0,A.jsx)("ul",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-list",role:"list",children:e.map(((e,t)=>(0,A.jsx)("li",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut",children:"string"==typeof e?(0,A.jsx)(rr,{name:e}):(0,A.jsx)(tr,{...e})},t)))}),sr=({title:e,shortcuts:t,className:r})=>(0,A.jsxs)("section",{className:j("edit-widgets-keyboard-shortcut-help-modal__section",r),children:[!!e&&(0,A.jsx)("h2",{className:"edit-widgets-keyboard-shortcut-help-modal__section-title",children:e}),(0,A.jsx)(ir,{shortcuts:t})]}),or=({title:e,categoryName:t,additionalShortcuts:r=[]})=>{const i=(0,l.useSelect)((e=>e(xt.store).getCategoryShortcuts(t)),[t]);return(0,A.jsx)(sr,{title:e,shortcuts:i.concat(r)})};function nr({isModalActive:e,toggleModal:t}){return(0,xt.useShortcut)("core/edit-widgets/keyboard-shortcuts",t,{bindGlobal:!0}),e?(0,A.jsxs)(S.Modal,{className:"edit-widgets-keyboard-shortcut-help-modal",title:(0,y.__)("Keyboard shortcuts"),onRequestClose:t,children:[(0,A.jsx)(sr,{className:"edit-widgets-keyboard-shortcut-help-modal__main-shortcuts",shortcuts:["core/edit-widgets/keyboard-shortcuts"]}),(0,A.jsx)(or,{title:(0,y.__)("Global shortcuts"),categoryName:"global"}),(0,A.jsx)(or,{title:(0,y.__)("Selection shortcuts"),categoryName:"selection"}),(0,A.jsx)(or,{title:(0,y.__)("Block shortcuts"),categoryName:"block",additionalShortcuts:[{keyCombination:{character:"/"},description:(0,y.__)("Change the block type after adding a new paragraph."),ariaLabel:(0,y.__)("Forward-slash")}]}),(0,A.jsx)(sr,{title:(0,y.__)("Text formatting"),shortcuts:Xt}),(0,A.jsx)(or,{title:(0,y.__)("List View shortcuts"),categoryName:"list-view"})]}):null}const{Fill:ar,Slot:cr}=(0,S.createSlotFill)("EditWidgetsToolsMoreMenuGroup");ar.Slot=({fillProps:e})=>(0,A.jsx)(cr,{fillProps:e,children:e=>e.length>0&&e});const dr=ar;function lr(){const[e,t]=(0,p.useState)(!1),r=()=>t(!e);(0,xt.useShortcut)("core/edit-widgets/keyboard-shortcuts",r);const i=(0,T.useViewportMatch)("medium");return(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)(S.DropdownMenu,{icon:Jt,label:(0,y.__)("Options"),popoverProps:{placement:"bottom-end",className:"more-menu-dropdown__content"},toggleProps:{tooltipPosition:"bottom",size:"compact"},children:e=>(0,A.jsxs)(A.Fragment,{children:[i&&(0,A.jsx)(S.MenuGroup,{label:(0,y._x)("View","noun"),children:(0,A.jsx)(_.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"fixedToolbar",label:(0,y.__)("Top toolbar"),info:(0,y.__)("Access all block and document tools in a single place"),messageActivated:(0,y.__)("Top toolbar activated"),messageDeactivated:(0,y.__)("Top toolbar deactivated")})}),(0,A.jsxs)(S.MenuGroup,{label:(0,y.__)("Tools"),children:[(0,A.jsx)(S.MenuItem,{onClick:()=>{t(!0)},shortcut:yt.displayShortcut.access("h"),children:(0,y.__)("Keyboard shortcuts")}),(0,A.jsx)(_.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"welcomeGuide",label:(0,y.__)("Welcome Guide")}),(0,A.jsxs)(S.MenuItem,{role:"menuitem",icon:Qt,href:(0,y.__)("https://wordpress.org/documentation/article/block-based-widgets-editor/"),target:"_blank",rel:"noopener noreferrer",children:[(0,y.__)("Help"),(0,A.jsx)(S.VisuallyHidden,{as:"span",children:(0,y.__)("(opens in a new tab)")})]}),(0,A.jsx)(dr.Slot,{fillProps:{onClose:e}})]}),(0,A.jsxs)(S.MenuGroup,{label:(0,y.__)("Preferences"),children:[(0,A.jsx)(_.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"keepCaretInsideBlock",label:(0,y.__)("Contain text cursor inside block"),info:(0,y.__)("Aids screen readers by stopping text caret from leaving blocks."),messageActivated:(0,y.__)("Contain text cursor inside block activated"),messageDeactivated:(0,y.__)("Contain text cursor inside block deactivated")}),(0,A.jsx)(_.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"themeStyles",info:(0,y.__)("Make the editor look like your theme."),label:(0,y.__)("Use theme styles")}),i&&(0,A.jsx)(_.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"showBlockBreadcrumbs",label:(0,y.__)("Display block breadcrumbs"),info:(0,y.__)("Shows block breadcrumbs at the bottom of the editor."),messageActivated:(0,y.__)("Display block breadcrumbs activated"),messageDeactivated:(0,y.__)("Display block breadcrumbs deactivated")})]})]})}),(0,A.jsx)(nr,{isModalActive:e,toggleModal:r})]})}const ur=function(){const e=(0,T.useViewportMatch)("medium"),t=(0,p.useRef)(),{hasFixedToolbar:r}=(0,l.useSelect)((e=>({hasFixedToolbar:!!e(_.store).get("core/edit-widgets","fixedToolbar")})),[]);return(0,A.jsx)(A.Fragment,{children:(0,A.jsxs)("div",{className:"edit-widgets-header",children:[(0,A.jsxs)("div",{className:"edit-widgets-header__navigable-toolbar-wrapper",children:[e&&(0,A.jsx)("h1",{className:"edit-widgets-header__title",children:(0,y.__)("Widgets")}),!e&&(0,A.jsx)(S.VisuallyHidden,{as:"h1",className:"edit-widgets-header__title",children:(0,y.__)("Widgets")}),(0,A.jsx)(Kt,{}),r&&e&&(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("div",{className:"selected-block-tools-wrapper",children:(0,A.jsx)(be.BlockToolbar,{hideDragHandle:!0})}),(0,A.jsx)(S.Popover.Slot,{ref:t,name:"block-toolbar"})]})]}),(0,A.jsxs)("div",{className:"edit-widgets-header__actions",children:[(0,A.jsx)(ce.Slot,{scope:"core/edit-widgets"}),(0,A.jsx)(qt,{}),(0,A.jsx)(lr,{})]})]})})};const gr=function(){const{removeNotice:e}=(0,l.useDispatch)(v.store),{notices:t}=(0,l.useSelect)((e=>({notices:e(v.store).getNotices()})),[]),r=t.filter((({isDismissible:e,type:t})=>e&&"default"===t)),i=t.filter((({isDismissible:e,type:t})=>!e&&"default"===t)),s=t.filter((({type:e})=>"snackbar"===e)).slice(-3);return(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)(S.NoticeList,{notices:i,className:"edit-widgets-notices__pinned"}),(0,A.jsx)(S.NoticeList,{notices:r,className:"edit-widgets-notices__dismissible",onRemove:e}),(0,A.jsx)(S.SnackbarList,{notices:s,className:"edit-widgets-notices__snackbar",onRemove:e})]})};function pr({blockEditorSettings:e}){const t=(0,l.useSelect)((e=>!!e(_.store).get("core/edit-widgets","themeStyles")),[]),r=(0,T.useViewportMatch)("medium"),i=(0,p.useMemo)((()=>t?e.styles:[]),[e,t]);return(0,A.jsxs)("div",{className:"edit-widgets-block-editor",children:[(0,A.jsx)(gr,{}),!r&&(0,A.jsx)(be.BlockToolbar,{hideDragHandle:!0}),(0,A.jsxs)(be.BlockTools,{children:[(0,A.jsx)(kt,{}),(0,A.jsx)(be.__unstableEditorStyles,{styles:i,scope:":where(.editor-styles-wrapper)"}),(0,A.jsx)(be.BlockSelectionClearer,{children:(0,A.jsx)(be.WritingFlow,{children:(0,A.jsx)(be.BlockList,{className:"edit-widgets-main-block-list"})})})]})]})}const hr=()=>{const e=(0,l.useSelect)((e=>{const{getEntityRecord:t}=e(m.store),r=t(ye,ke,"widget-areas");return r?.blocks[0]?.clientId}),[]);return(0,l.useSelect)((t=>{const{getBlockRootClientId:r,getBlockSelectionEnd:i,getBlockOrder:s,getBlockIndex:o}=t(be.store),n=t(nt).__experimentalGetInsertionPoint();if(n.rootClientId)return n;const a=i()||e,c=r(a);return a&&""===c?{rootClientId:a,insertionIndex:s(a).length}:{rootClientId:c,insertionIndex:o(a)+1}}),[e])};function mr(){const e=(0,T.useViewportMatch)("medium","<"),{rootClientId:t,insertionIndex:r}=hr(),{setIsInserterOpened:i}=(0,l.useDispatch)(nt),s=(0,p.useCallback)((()=>i(!1)),[i]),[o,n]=(0,T.__experimentalUseDialog)({onClose:s,focusOnMount:!0}),a=(0,p.useRef)();return(0,A.jsx)("div",{ref:o,...n,className:"edit-widgets-layout__inserter-panel",children:(0,A.jsx)("div",{className:"edit-widgets-layout__inserter-panel-content",children:(0,A.jsx)(be.__experimentalLibrary,{showInserterHelpPanel:!0,shouldFocusBlock:e,rootClientId:t,__experimentalInsertionIndex:r,ref:a,onClose:s})})})}function wr(){const{setIsListViewOpened:e}=(0,l.useDispatch)(nt),{getListViewToggleRef:t}=st((0,l.useSelect)(nt)),[r,i]=(0,p.useState)(null),s=(0,T.useFocusOnMount)("firstElement"),o=(0,p.useCallback)((()=>{e(!1),t().current?.focus()}),[t,e]),n=(0,p.useCallback)((e=>{e.keyCode!==yt.ESCAPE||e.defaultPrevented||(e.preventDefault(),o())}),[o]);return(0,A.jsxs)("div",{className:"edit-widgets-editor__list-view-panel",onKeyDown:n,children:[(0,A.jsxs)("div",{className:"edit-widgets-editor__list-view-panel-header",children:[(0,A.jsx)("strong",{children:(0,y.__)("List View")}),(0,A.jsx)(S.Button,{icon:R,label:(0,y.__)("Close"),onClick:o,size:"compact"})]}),(0,A.jsx)("div",{className:"edit-widgets-editor__list-view-panel-content",ref:(0,T.useMergeRefs)([s,i]),children:(0,A.jsx)(be.__experimentalListView,{dropZoneElement:r})})]})}function _r(){const{isInserterOpen:e,isListViewOpen:t}=(0,l.useSelect)((e=>{const{isInserterOpened:t,isListViewOpened:r}=e(nt);return{isInserterOpen:t(),isListViewOpen:r()}}),[]);return e?(0,A.jsx)(mr,{}):t?(0,A.jsx)(wr,{}):null}const br={header:(0,y.__)("Widgets top bar"),body:(0,y.__)("Widgets and blocks"),sidebar:(0,y.__)("Widgets settings"),footer:(0,y.__)("Widgets footer")};const fr=function({blockEditorSettings:e}){const t=(0,T.useViewportMatch)("medium","<"),r=(0,T.useViewportMatch)("huge",">="),{setIsInserterOpened:i,setIsListViewOpened:s,closeGeneralSidebar:o}=(0,l.useDispatch)(nt),{hasBlockBreadCrumbsEnabled:n,hasSidebarEnabled:a,isInserterOpened:c,isListViewOpened:d}=(0,l.useSelect)((e=>({hasSidebarEnabled:!!e(X).getActiveComplementaryArea(nt.name),isInserterOpened:!!e(nt).isInserterOpened(),isListViewOpened:!!e(nt).isListViewOpened(),hasBlockBreadCrumbsEnabled:!!e(_.store).get("core/edit-widgets","showBlockBreadcrumbs")})),[]);(0,p.useEffect)((()=>{a&&!r&&(i(!1),s(!1))}),[a,r]),(0,p.useEffect)((()=>{!c&&!d||r||o()}),[c,d,r]);const u=d?(0,y.__)("List View"):(0,y.__)("Block Library"),g=d||c;return(0,A.jsx)(_e,{labels:{...br,secondarySidebar:u},header:(0,A.jsx)(ur,{}),secondarySidebar:g&&(0,A.jsx)(_r,{}),sidebar:(0,A.jsx)(ge.Slot,{scope:"core/edit-widgets"}),content:(0,A.jsx)(A.Fragment,{children:(0,A.jsx)(pr,{blockEditorSettings:e})}),footer:n&&!t&&(0,A.jsx)("div",{className:"edit-widgets-layout__footer",children:(0,A.jsx)(be.BlockBreadcrumb,{rootLabelText:(0,y.__)("Widgets")})})})};function xr(){const e=(0,l.useSelect)((e=>{const{getEditedWidgetAreas:t}=e(nt),r=t();return r?.length>0}),[]);return(0,p.useEffect)((()=>{const t=t=>{if(e)return t.returnValue=(0,y.__)("You have unsaved changes. If you proceed, they will be lost."),t.returnValue};return window.addEventListener("beforeunload",t),()=>{window.removeEventListener("beforeunload",t)}}),[e]),null}function yr(){var e;const t=(0,l.useSelect)((e=>!!e(_.store).get("core/edit-widgets","welcomeGuide")),[]),{toggle:r}=(0,l.useDispatch)(_.store),i=(0,l.useSelect)((e=>e(nt).getWidgetAreas({per_page:-1})),[]);if(!t)return null;const s=i?.every((e=>"wp_inactive_widgets"===e.id||e.widgets.every((e=>e.startsWith("block-"))))),o=null!==(e=i?.filter((e=>"wp_inactive_widgets"!==e.id)).length)&&void 0!==e?e:0;return(0,A.jsx)(S.Guide,{className:"edit-widgets-welcome-guide",contentLabel:(0,y.__)("Welcome to block Widgets"),finishButtonText:(0,y.__)("Get started"),onFinish:()=>r("core/edit-widgets","welcomeGuide"),pages:[{image:(0,A.jsx)(vr,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.gif"}),content:(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("h1",{className:"edit-widgets-welcome-guide__heading",children:(0,y.__)("Welcome to block Widgets")}),s?(0,A.jsx)(A.Fragment,{children:(0,A.jsx)("p",{className:"edit-widgets-welcome-guide__text",children:(0,y.sprintf)((0,y._n)("Your theme provides %s “block” area for you to add and edit content. Try adding a search bar, social icons, or other types of blocks here and see how they’ll look on your site.","Your theme provides %s different “block” areas for you to add and edit content. Try adding a search bar, social icons, or other types of blocks here and see how they’ll look on your site.",o),o)})}):(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("p",{className:"edit-widgets-welcome-guide__text",children:(0,y.__)("You can now add any block to your site’s widget areas. Don’t worry, all of your favorite widgets still work flawlessly.")}),(0,A.jsxs)("p",{className:"edit-widgets-welcome-guide__text",children:[(0,A.jsx)("strong",{children:(0,y.__)("Want to stick with the old widgets?")})," ",(0,A.jsx)(S.ExternalLink,{href:(0,y.__)("https://wordpress.org/plugins/classic-widgets/"),children:(0,y.__)("Get the Classic Widgets plugin.")})]})]})]})},{image:(0,A.jsx)(vr,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-editor.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-editor.gif"}),content:(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("h1",{className:"edit-widgets-welcome-guide__heading",children:(0,y.__)("Customize each block")}),(0,A.jsx)("p",{className:"edit-widgets-welcome-guide__text",children:(0,y.__)("Each block comes with its own set of controls for changing things like color, width, and alignment. These will show and hide automatically when you have a block selected.")})]})},{image:(0,A.jsx)(vr,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-library.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-library.gif"}),content:(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("h1",{className:"edit-widgets-welcome-guide__heading",children:(0,y.__)("Explore all blocks")}),(0,A.jsx)("p",{className:"edit-widgets-welcome-guide__text",children:(0,p.createInterpolateElement)((0,y.__)("All of the blocks available to you live in the block library. You’ll find it wherever you see the <InserterIconImage /> icon."),{InserterIconImage:(0,A.jsx)("img",{className:"edit-widgets-welcome-guide__inserter-icon",alt:(0,y.__)("inserter"),src:"data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='18' height='18' rx='2' fill='%231E1E1E'/%3E%3Cpath d='M9.22727 4V14M4 8.77273H14' stroke='white' stroke-width='1.5'/%3E%3C/svg%3E%0A"})})})]})},{image:(0,A.jsx)(vr,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.gif"}),content:(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("h1",{className:"edit-widgets-welcome-guide__heading",children:(0,y.__)("Learn more")}),(0,A.jsx)("p",{className:"edit-widgets-welcome-guide__text",children:(0,p.createInterpolateElement)((0,y.__)("New to the block editor? Want to learn more about using it? <a>Here's a detailed guide.</a>"),{a:(0,A.jsx)(S.ExternalLink,{href:(0,y.__)("https://wordpress.org/documentation/article/wordpress-block-editor/")})})})]})}]})}function vr({nonAnimatedSrc:e,animatedSrc:t}){return(0,A.jsxs)("picture",{className:"edit-widgets-welcome-guide__image",children:[(0,A.jsx)("source",{srcSet:e,media:"(prefers-reduced-motion: reduce)"}),(0,A.jsx)("img",{src:t,width:"312",height:"240",alt:""})]})}const kr=function({blockEditorSettings:e}){const{createErrorNotice:t}=(0,l.useDispatch)(v.store),r=(0,S.__unstableUseNavigateRegions)();return(0,A.jsx)(bt,{children:(0,A.jsx)("div",{className:r.className,...r,ref:r.ref,children:(0,A.jsxs)(Ct,{blockEditorSettings:e,children:[(0,A.jsx)(fr,{blockEditorSettings:e}),(0,A.jsx)(Gt,{}),(0,A.jsx)(L.PluginArea,{onError:function(e){t((0,y.sprintf)((0,y.__)('The "%s" plugin has encountered an error and cannot be rendered.'),e))}}),(0,A.jsx)(xr,{}),(0,A.jsx)(yr,{})]})})})},jr=["core/more","core/freeform","core/template-part","core/block"];function Sr(e,t){const r=document.getElementById(e),i=(0,p.createRoot)(r),s=(0,h.__experimentalGetCoreBlocks)().filter((e=>!(jr.includes(e.name)||e.name.startsWith("core/post")||e.name.startsWith("core/query")||e.name.startsWith("core/site")||e.name.startsWith("core/navigation"))));return(0,l.dispatch)(_.store).setDefaults("core/edit-widgets",{fixedToolbar:!1,welcomeGuide:!0,showBlockBreadcrumbs:!0,themeStyles:!0}),(0,l.dispatch)(d.store).reapplyBlockTypeFilters(),(0,h.registerCoreBlocks)(s),(0,w.registerLegacyWidgetBlock)(),(0,w.registerLegacyWidgetVariations)(t),Ir(c),(0,w.registerWidgetGroupBlock)(),t.__experimentalFetchLinkSuggestions=(e,r)=>(0,m.__experimentalFetchLinkSuggestions)(e,r,t),(0,d.setFreeformContentHandlerName)("core/html"),i.render((0,A.jsx)(p.StrictMode,{children:(0,A.jsx)(kr,{blockEditorSettings:t})})),i}const Er=Sr;function Ar(){g()("wp.editWidgets.reinitializeEditor",{since:"6.2",version:"6.3"})}const Ir=e=>{if(!e)return;const{metadata:t,settings:r,name:i}=e;t&&(0,d.unstable__bootstrapServerSideBlockDefinitions)({[i]:t}),(0,d.registerBlockType)(i,r)};(window.wp=window.wp||{}).editWidgets=t})();