{"version": 3, "sources": ["parsley.min.js", "/source/parsley.js", "/source/src/parsley/pubsub.js", "/source/src/parsley/utils.js", "/source/src/parsley/defaults.js", "/source/src/parsley/abstract.js", "/source/src/parsley/validator.js", "/source/src/parsley/validator_registry.js", "/source/src/parsley/ui.js", "/source/src/parsley/form.js", "/source/src/parsley/factory/constraint.js", "/source/src/parsley/field.js", "/source/src/parsley/multiple.js", "/source/src/parsley/factory.js", "/source/src/parsley/main.js", "/source/src/parsley/remote.js", "/source/src/i18n/en.js", "/source/src/parsley.js"], "names": ["_toConsumableArray", "arr", "Array", "isArray", "i", "arr2", "length", "from", "_slice", "prototype", "slice", "global", "factory", "exports", "module", "require", "define", "amd", "parsley", "j<PERSON><PERSON><PERSON>", "this", "$", "adapt", "fn", "context", "parsleyAdapted<PERSON>allback", "args", "call", "arguments", "unshift", "apply", "o", "eventName", "name", "lastIndexOf", "eventPrefix", "substr", "globalID", "pastWarnings", "Pars<PERSON><PERSON>tils__Pa<PERSON><PERSON><PERSON>tils", "attr", "$element", "namespace", "obj", "attribute", "attributes", "regex", "RegExp", "hasOwnProperty", "specified", "test", "camelize", "deserializeValue", "value", "checkAttr", "_checkAttr", "is", "setAttr", "setAttribute", "dasherize", "String", "generateID", "num", "isNaN", "Number", "parseJSON", "e", "str", "replace", "match", "chr", "toUpperCase", "toLowerCase", "warn", "_window$console", "window", "console", "warnOnce", "msg", "_resetWarnings", "trimString", "string", "namespaceEvents", "events", "split", "map", "evt", "join", "objectCreate", "Object", "create", "Error", "TypeError", "result", "ParsleyUtils__default", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs", "excluded", "priorityEnabled", "multiple", "group", "uiEnabled", "validation<PERSON>hreshold", "focus", "trigger", "triggerAfterFailure", "errorClass", "successClass", "classHandler", "<PERSON><PERSON><PERSON>F<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "errorsWrapper", "errorTemplate", "ParsleyAbstract", "asyncSupport", "actualizeOptions", "options", "domOptions", "parent", "_resetOptions", "initOptions", "_listeners", "on", "queue", "push", "subscribe", "listenTo", "off", "splice", "unsubscribe", "unsubscribeTo", "target", "extraArg", "reset", "__class__", "_resetUI", "_trigger", "fields", "destroy", "_destroyUI", "removeData", "asyncIsValid", "force", "<PERSON><PERSON><PERSON><PERSON>", "_findRelated", "find", "requirementConverters", "_string", "integer", "parseInt", "number", "parseFloat", "reference", "boolean", "object", "regexp", "_regexp", "flags", "convertArrayRequirement", "m", "values", "convertRequirement", "requirementType", "converter", "convertExtraOptionRequirement", "requirementSpec", "extraOptionReader", "main", "extra", "key", "<PERSON><PERSON><PERSON>Valida<PERSON>", "spec", "extend", "validate", "requirementFirstArg", "validate<PERSON><PERSON><PERSON><PERSON>", "validateNumber", "validateString", "parseRequirements", "requirements", "type", "isPlainObject", "priority", "ParsleyValidatorRegistry", "validators", "catalog", "locale", "init", "typeRegexes", "email", "digits", "alphanum", "url", "range", "decimalPlaces", "Math", "max", "addValidator", "<PERSON><PERSON><PERSON>", "setLocale", "addCatalog", "messages", "set", "addMessage", "message", "addMessages", "nameMessageObject", "arg1", "arg2", "_setValidator", "updateValidator", "removeValidator", "validator", "getErrorMessage", "constraint", "typeMessages", "formatMessage", "defaultMessage", "en", "parameters", "notblank", "required", "_ref", "undefined", "_ref$step", "step", "_ref$base", "base", "nb", "decimals", "toInt", "f", "round", "pow", "pattern", "minlength", "requirement", "maxlength", "min", "mincheck", "maxcheck", "check", "equalto", "refOr<PERSON><PERSON>ue", "$reference", "val", "ParsleyUI", "diffResults", "newResult", "oldResult", "deep", "added", "kept", "found", "j", "assert", "removed", "Form", "_actualizeTriggers", "_this", "onSubmitValidate", "onSubmitButton", "_focusedField", "validationResult", "field", "noFocus", "Field", "_reflowUI", "_buildUI", "_ui", "diff", "lastValidationResult", "_manageStatusClass", "_manageErrorsMessages", "_failedOnce", "getErrorsMessages", "errorMessage", "_getErrorMessage", "addError", "_ref2", "_ref2$updateClass", "updateClass", "_addError", "_errorClass", "updateError", "_ref3", "_ref3$updateClass", "_updateError", "removeError", "_ref4", "_ref4$updateClass", "_removeError", "hasConstraints", "needsValidation", "_successClass", "_resetClass", "errorsMessagesDisabled", "_insertErrorWrapper", "$errorsWrapper", "append", "addClass", "html", "removeClass", "remove", "_ref5", "_ref6", "customConstraintErrorMessage", "__id__", "$errorClassHandler", "_manageClassHandler", "errorsWrapperId", "validationInformationVisible", "$handler", "$errorsContainer", "$from", "after", "_this2", "$toBind", "event", "_eventValidate", "getValue", "children", "ParsleyForm", "element", "ParsleyForm__statusMapping", "pending", "resolved", "rejected", "_this3", "$submitSource", "_$submitSource", "first", "prop", "promise", "whenValidate", "state", "stopImmediatePropagation", "preventDefault", "done", "_submit", "$synthetic", "appendTo", "Event", "_arguments", "_this4", "_ref7", "submitEvent", "_refreshFields", "promises", "_withoutReactualizingFormOptions", "promiseBasedOnValidationResult", "r", "Deferred", "reject", "resolve", "when", "fail", "always", "pipe", "<PERSON><PERSON><PERSON><PERSON>", "_arguments2", "_this5", "_ref8", "_bindFields", "_this6", "old<PERSON>ields", "fieldsMappedById", "not", "each", "_", "fieldInstance", "Factory", "oldActualizeOptions", "ConstraintFactory", "parsleyField", "isDomConstraint", "validatorSpec", "_validatorRegistry", "_parseRequirements", "capitalize", "cap", "instance", "requirementList", "_this7", "parsleyFormInstance", "constraints", "constraintsByName", "_bindConstraints", "parsley_field__statusMapping", "_this8", "_ref9", "refreshConstraints", "_isInGroup", "_refreshed", "_isRequired", "validateIfEmpty", "inArray", "_arguments3", "_this9", "_ref10", "_ref10$force", "groupedConstraints", "_getGroupedConstraints", "_validateConstraint", "_this10", "_handleWhitespace", "addConstraint", "removeConstraint", "updateConstraint", "_bindHtml5Constraints", "hasClass", "trimValue", "whitespace", "index", "p", "sort", "a", "b", "parsley_field", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addElement", "$elements", "fieldConstraints", "has", "data", "filter", "_init", "ParsleyFactory", "savedparsleyFormInstance", "__version__", "bind", "isMultiple", "handleMultiple", "parsleyMultipleInstance", "_this11", "input", "$previouslyRelated", "get", "doNotStore", "parsleyInstance", "<PERSON><PERSON>leyExtend", "vernums", "j<PERSON>y", "for<PERSON>ach", "document", "version", "psly", "instances", "ParsleyConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registry", "i18n", "method", "proxy", "_window$Parsley", "UI", "doNotUpdateClass", "navigator", "userAgent", "autoBind", "deprecated", "listen", "callback", "unsubscribeAll", "emit", "_instance", "instanceGiven", "asyncValidators", "default", "xhr", "status", "reverse", "addAsyncValidator", "ajaxOptions", "csr", "indexOf", "encodeURIComponent", "remoteOptions", "param", "_remoteCache", "ajax", "handleXhr", "then"], "mappings": ";;;;;;;;AAcA,QAASA,oBAAmBC,GAAO,GAAIC,MAAMC,QAAQF,GAAM,CAAE,IAAK,GAAIG,GAAI,EAAGC,EAAOH,MAAMD,EAAIK,QAASF,EAAIH,EAAIK,OAAQF,IAAKC,EAAKD,GAAKH,EAAIG,EAAI,OAAOC,GAAe,MAAOH,OAAMK,KAAKN,GCFtL,GAAAO,QAAAN,MAAAO,UAAAC,OAZA,SAAWC,EAAQC,GACE,gBAAZC,UAA0C,mBAAXC,QAAyBA,OAAOD,QAAUD,EAAQG,QAAQ,WAC9E,kBAAXC,SAAyBA,OAAOC,IAAMD,QAAQ,UAAWJ,GAChED,EAAOO,QAAUN,EAAQD,EAAOQ,SAChCC,KAAM,SAAUC,GAAK,YCOvB,SAASC,GAAMC,EAAIC,GASjB,MAPKD,GAAGE,yBACNF,EAAGE,uBAAyB,WAC1B,GAAIC,GAAOxB,MAAMO,UAAUC,MAAMiB,KAAKC,UAAW,EACjDF,GAAKG,QAAQT,MACbG,EAAGO,MAAMN,GAAWO,EAAGL,KAGpBH,EAAGE,uBAKZ,QAASO,GAAUC,GACjB,MAAyC,KAArCA,EAAKC,YAAYC,EAAa,GACzBF,EAAKG,OAAOD,EAAY7B,QAC1B2B,EC1BT,GAAII,GAAW,EACXC,KAHJC,GAQEC,KAAM,SAAUC,EAAUC,EAAWC,GACnC,GAAIvC,GACAwC,EACAC,EACAC,EAAQ,GAAIC,QAAO,IAAML,EAAW,IAExC,IAAI,mBAAuBC,GACzBA,SAGA,KAAKvC,IAAKuC,GACJA,EAAIK,eAAe5C,UACduC,GAAIvC,EAIjB,IAAI,mBAAuBqC,IAAY,mBAAuBA,GAAS,GACrE,MAAOE,EAGT,KADAE,EAAaJ,EAAS,GAAGI,WACpBzC,EAAIyC,EAAWvC,OAAQF,KAC1BwC,EAAYC,EAAWzC,GAEnBwC,GAAaA,EAAUK,WAAaH,EAAMI,KAAKN,EAAUX,QAC3DU,EAAIvB,KAAK+B,SAASP,EAAUX,KAAKvB,MAAMgC,EAAUpC,UAAYc,KAAKgC,iBAAiBR,EAAUS,OAIjG,OAAOV,IAGTW,UAAW,SAAUb,EAAUC,EAAWa,GACxC,MAAOd,GAASe,GAAG,IAAMd,EAAYa,EAAY,MAGnDE,QAAS,SAAUhB,EAAUC,EAAWF,EAAMa,GAC5CZ,EAAS,GAAGiB,aAAatC,KAAKuC,UAAUjB,EAAYF,GAAOoB,OAAOP,KAGpEQ,WAAY,WACV,MAAO,GAAKxB,KAKde,iBAAkB,SAAUC,GAC1B,GAAIS,EAEJ,KACE,MAAOT,GACI,QAATA,IACU,SAATA,GAAmB,EACX,QAATA,EAAkB,KACjBU,MAAMD,EAAME,OAAOX,IACpB,UAAUH,KAAKG,GAAShC,EAAE4C,UAAUZ,GACpCA,EAF8BS,GAG5BT,EACJ,MAAOa,GAAK,MAAOb,KAIvBF,SAAU,SAAUgB,GAClB,MAAOA,GAAIC,QAAQ,UAAW,SAAUC,EAAOC,GAC7C,MAAOA,GAAMA,EAAIC,cAAgB,MAKrCZ,UAAW,SAAUQ,GACnB,MAAOA,GAAIC,QAAQ,MAAO,KACvBA,QAAQ,wBAAyB,SACjCA,QAAQ,oBAAqB,SAC7BA,QAAQ,KAAM,KACdI,eAGLC,KAAM,WHOF,GAAIC,EGNFC,QAAOC,SAAW,kBAAsBD,QAAOC,QAAQH,OACzDC,EAAAC,OAAOC,SAAQH,KAAA3C,MAAA4C,EAAQ9C,YAG3BiD,SAAU,SAASC,GACZxC,EAAawC,KAChBxC,EAAawC,IAAO,EACpB1D,KAAKqD,KAAA3C,MAALV,KAAaQ,aAIjBmD,eAAgB,WACdzC,MAGF0C,WAAY,SAASC,GACnB,MAAOA,GAAOb,QAAQ,aAAc,KAGtCc,gBAAiB,SAASC,EAAQzC,GAEhC,MADAyC,GAAS/D,KAAK4D,WAAWG,GAAU,IAAIC,MAAM,OACxCD,EAAO,GAEL9D,EAAEgE,IAAIF,EAAQ,SAAAG,GAAS,MAAUA,GAAA,IAAO5C,IAAgB6C,KAAK,KAD3D,IAKXC,aAAcC,OAAOC,QAAU,WAC7B,GAAID,GAAS,YACb,OAAO,UAAUhF,GACf,GAAImB,UAAUtB,OAAS,EACrB,KAAMqF,OAAM,gCAEd,IAAwB,gBAAblF,GACT,KAAMmF,WAAU,6BAElBH,GAAOhF,UAAYA,CACnB,IAAIoF,GAAS,GAAIJ,EAEjB,OADAA,GAAOhF,UAAY,KACZoF,OA5HbC,EAAAvD,ECKIwD,GAIFrD,UAAW,gBAGXsD,OAAQ,0BAGRC,SAAU,gFAGVC,iBAAiB,EAKjBC,SAAU,KAGVC,MAAO,KAIPC,WAAW,EAGXC,oBAAqB,EAGrBC,MAAO,QAGPC,SAAS,EAGTC,oBAAqB,QAGrBC,WAAY,gBAGZC,aAAc,kBAIdC,aAAc,SAAUC,KAIxBC,gBAAiB,SAAUD,KAG3BE,cAAe,wCAGfC,cAAe,aC3DbC,EAAkB,YAEtBA,GAAgBxG,WACdyG,cAAc,EAEdC,iBAAkB,WAIhB,MAZJrB,GASiBtD,KAAKpB,KAAKqB,SAAUrB,KAAKgG,QAAQ1E,UAAWtB,KAAKiG,YAC1DjG,KAAKkG,QAAUlG,KAAKkG,OAAOH,kBAC7B/F,KAAKkG,OAAOH,mBACP/F,MAGTmG,cAAe,SAAUC,GACvBpG,KAAKiG,WAhBTvB,EAgBmCN,aAAapE,KAAKkG,OAAOF,SACxDhG,KAAKgG,QAjBTtB,EAiBgCN,aAAapE,KAAKiG,WAE9C,KAAK,GAAIjH,KAAKoH,GACRA,EAAYxE,eAAe5C,KAC7BgB,KAAKgG,QAAQhH,GAAKoH,EAAYpH,GAElCgB,MAAK+F,oBAGPM,WAAY,KAMZC,GAAI,SAAUzF,EAAMV,GAClBH,KAAKqG,WAAarG,KAAKqG,cACvB,IAAIE,GAAQvG,KAAKqG,WAAWxF,GAAQb,KAAKqG,WAAWxF,MAGpD,OAFA0F,GAAMC,KAAKrG,GAEJH,MAITyG,UAAW,SAAS5F,EAAMV,GACxBF,EAAEyG,SAAS1G,KAAMa,EAAKuC,cAAejD,IAIvCwG,IAAK,SAAU9F,EAAMV,GACnB,GAAIoG,GAAQvG,KAAKqG,YAAcrG,KAAKqG,WAAWxF,EAC/C,IAAI0F,EACF,GAAKpG,EAGH,IAAK,GAAInB,GAAIuH,EAAMrH,OAAQF,KACrBuH,EAAMvH,KAAOmB,GACfoG,EAAMK,OAAO5H,EAAG,cAJbgB,MAAKqG,WAAWxF,EAO3B,OAAOb,OAIT6G,YAAa,SAAShG,EAAMV,GAC1BF,EAAE6G,cAAc9G,KAAMa,EAAKuC,gBAM7BgC,QAAS,SAAUvE,EAAMkG,EAAQC,GAC/BD,EAASA,GAAU/G,IACnB,IACIyE,GADA8B,EAAQvG,KAAKqG,YAAcrG,KAAKqG,WAAWxF,EAG/C,IAAI0F,EACF,IAAK,GAAIvH,GAAIuH,EAAMrH,OAAQF,KAEzB,GADAyF,EAAS8B,EAAMvH,GAAGuB,KAAKwG,EAAQA,EAAQC,GACnCvC,KAAW,EAAO,MAAOA,EAGjC,OAAIzE,MAAKkG,OACAlG,KAAKkG,OAAOd,QAAQvE,EAAMkG,EAAQC,IAEpC,GAITC,MAAO,WAEL,GAAI,gBAAkBjH,KAAKkH,UAEzB,MADAlH,MAAKmH,WACEnH,KAAKoH,SAAS,QAIvB,KAAK,GAAIpI,GAAI,EAAGA,EAAIgB,KAAKqH,OAAOnI,OAAQF,IACtCgB,KAAKqH,OAAOrI,GAAGiI,OAEjBjH,MAAKoH,SAAS,UAIhBE,QAAS,WAGP,GADAtH,KAAKuH,aACD,gBAAkBvH,KAAKkH,UAKzB,MAJAlH,MAAKqB,SAASmG,WAAW,WACzBxH,KAAKqB,SAASmG,WAAW,4BACzBxH,MAAKoH,SAAS,UAMhB,KAAK,GAAIpI,GAAI,EAAGA,EAAIgB,KAAKqH,OAAOnI,OAAQF,IACtCgB,KAAKqH,OAAOrI,GAAGsI,SAEjBtH,MAAKqB,SAASmG,WAAW,WACzBxH,KAAKoH,SAAS,YAGhBK,aAAc,SAAUzC,EAAO0C,GAE7B,MA1HJhD,GAyHiBjB,SAAS,4DACfzD,KAAK2H,WAAW3C,MAAAA,EAAO0C,MAAAA,KAGhCE,aAAc,WACZ,MAAO5H,MAAKgG,QAAQjB,SAClB/E,KAAKkG,OAAO7E,SAASwG,KAAA,IAAS7H,KAAKgG,QAAQ1E,UAAA,aAAsBtB,KAAKgG,QAAQjB,SAAA,MAC9E/E,KAAKqB,UC7HX,IAAIyG,IACFjE,OAAQ,SAASkE,GACf,MAAOA,IAETC,QAAS,SAASnE,GAChB,GAAIlB,MAAMkB,GACR,KAAM,mCAAqCA,EAAS,GACtD,OAAOoE,UAASpE,EAAQ,KAE1BqE,OAAQ,SAASrE,GACf,GAAIlB,MAAMkB,GACR,KAAM,iCAAmCA,EAAS,GACpD,OAAOsE,YAAWtE,IAEpBuE,UAAW,SAASvE,GAClB,GAAIY,GAASxE,EAAE4D,EACf,IAAsB,IAAlBY,EAAOvF,OACT,KAAM,uBAAyB2E,EAAS,GAC1C,OAAOY,IAET4D,UAAS,SAASxE,GAChB,MAAkB,UAAXA,GAETyE,OAAQ,SAASzE,GACf,MA3BJa,GA2BwB1C,iBAAiB6B,IAEvC0E,OAAQ,SAASC,GACf,GAAIC,GAAQ,EAcZ,OAXI,sBAAsB3G,KAAK0G,IAG7BC,EAAQD,EAAOxF,QAAQ,iBAAkB,MAGzCwF,EAASA,EAAOxF,QAAQ,GAAIrB,QAAO,WAAa8G,EAAQ,KAAM,OAG9DD,EAAS,IAAMA,EAAS,IAEnB,GAAI7G,QAAO6G,EAAQC,KAI1BC,EAA0B,SAAS7E,EAAQ3E,GAC7C,GAAIyJ,GAAI9E,EAAOZ,MAAM,mBACrB,KAAK0F,EACH,KAAM,iCAAmC9E,EAAS,GACpD,IAAI+E,GAASD,EAAE,GAAG3E,MAAM,KAAKC,IApD/BS,EAoDgDd,WAC9C,IAAIgF,EAAO1J,SAAWA,EACpB,KAAM,mBAAqB0J,EAAO1J,OAAS,gBAAkBA,EAAS,aACxE,OAAO0J,IAGLC,EAAqB,SAASC,EAAiBjF,GACjD,GAAIkF,GAAYjB,EAAsBgB,GAAmB,SACzD,KAAKC,EACH,KAAM,uCAAyCD,EAAkB,GACnE,OAAOC,GAAUlF,IAGfmF,EAAgC,SAASC,EAAiBpF,EAAQqF,GACpE,GAAIC,GAAO,KACPC,IACJ,KAAK,GAAIC,KAAOJ,GACd,GAAII,EAAK,CACP,GAAIpH,GAAQiH,EAAkBG,EAC1B,iBAAoBpH,KACtBA,EAAQ4G,EAAmBI,EAAgBI,GAAMpH,IACnDmH,EAAMC,GAAOpH,MAEbkH,GAAON,EAAmBI,EAAgBI,GAAMxF,EAGpD,QAAQsF,EAAMC,IAKZE,EAAmB,SAASC,GAC9BtJ,EAAEuJ,QAAO,EAAMxJ,KAAMuJ,GAGvBD,GAAiBjK,WAEfoK,SAAU,SAASxH,EAAOyH,GACxB,GAAI1J,KAAKG,GAIP,MAFIK,WAAUtB,OAAS,IACrBwK,KAAyBpK,MAAMiB,KAAKC,UAAW,EAAG,KAC7CR,KAAKG,GAAGI,KAAKP,KAAMiC,EAAOyH,EAGnC,IAAIzJ,EAAElB,QAAQkD,GAAQ,CACpB,IAAKjC,KAAK2J,iBACR,KAAM,cAAgB3J,KAAKa,KAAO,mCACpC,OAAOb,MAAK2J,iBAAAjJ,MAALV,KAAyBQ,WAEhC,GAAIR,KAAK4J,eACP,MAAIjH,OAAMV,IACD,GACTzB,UAAU,GAAK2H,WAAW3H,UAAU,IAC7BR,KAAK4J,eAAAlJ,MAALV,KAAuBQ,WAEhC,IAAIR,KAAK6J,eACP,MAAO7J,MAAK6J,eAAAnJ,MAALV,KAAuBQ,UAEhC,MAAM,cAAgBR,KAAKa,KAAO,kCAMtCiJ,kBAAmB,SAASC,EAAcb,GACxC,GAAI,gBAAoBa,GAGtB,MAAO9J,GAAElB,QAAQgL,GAAgBA,GAAgBA,EAEnD,IAAIC,GAAOhK,KAAK8I,eAChB,IAAI7I,EAAElB,QAAQiL,GAAO,CAEnB,IAAK,GADDpB,GAASF,EAAwBqB,EAAcC,EAAK9K,QAC/CF,EAAI,EAAGA,EAAI4J,EAAO1J,OAAQF,IACjC4J,EAAO5J,GAAK6J,EAAmBmB,EAAKhL,GAAI4J,EAAO5J,GACjD,OAAO4J,GACF,MAAI3I,GAAEgK,cAAcD,GAClBhB,EAA8BgB,EAAMD,EAAcb,IAEjDL,EAAmBmB,EAAMD,KAIrCjB,gBAAiB,SAEjBoB,SAAU,ECrIZ,IAAIC,GAA2B,SAAUC,EAAYC,GACnDrK,KAAKkH,UAAY,2BAGjBlH,KAAKsK,OAAS,KAEdtK,KAAKuK,KAAKH,MAAkBC,QAG1BG,GACFC,MAAO,04BAGPvC,OAAQ,+BAERF,QAAS,UAET0C,OAAQ,QAERC,SAAU,SAEVC,IAAK,GAAIjJ,QACL,qWA+BK,KAGX6I,GAAYK,MAAQL,EAAYtC,MAGhC,IAAI4C,GAAgB,SAAApI,GAClB,GAAIO,IAAS,GAAKP,GAAKO,MAAM,mCAC7B,OAAKA,GACE8H,KAAKC,IACP,GAEC/H,EAAM,GAAKA,EAAM,GAAG/D,OAAS,IAE7B+D,EAAM,IAAMA,EAAM,GAAK,IANR,EASvBkH,GAAyB9K,WACvBkL,KAAM,SAAUH,EAAYC,GAC1BrK,KAAKqK,QAAUA,EAEfrK,KAAKoK,WAAanK,EAAEuJ,UAAWxJ,KAAKoK,WAEpC,KAAK,GAAIvJ,KAAQuJ,GACfpK,KAAKiL,aAAapK,EAAMuJ,EAAWvJ,GAAMV,GAAIiK,EAAWvJ,GAAMqJ,SAEhE3G,QAAO2H,QAAQ9F,QAAQ,2BAIzB+F,UAAW,SAAUb,GACnB,GAAI,mBAAuBtK,MAAKqK,QAAQC,GACtC,KAAM,IAAI/F,OAAM+F,EAAS,mCAI3B,OAFAtK,MAAKsK,OAASA,EAEPtK,MAIToL,WAAY,SAAUd,EAAQe,EAAUC,GAItC,MAHI,gBAAoBD,KACtBrL,KAAKqK,QAAQC,GAAUe,IAErB,IAASC,EACJtL,KAAKmL,UAAUb,GAEjBtK,MAITuL,WAAY,SAAUjB,EAAQzJ,EAAM2K,GAMlC,MALI,mBAAuBxL,MAAKqK,QAAQC,KACtCtK,KAAKqK,QAAQC,OAEftK,KAAKqK,QAAQC,GAAQzJ,GAAQ2K,EAEtBxL,MAITyL,YAAa,SAAUnB,EAAQoB,GAC7B,IAAK,GAAI7K,KAAQ6K,GACf1L,KAAKuL,WAAWjB,EAAQzJ,EAAM6K,EAAkB7K,GAElD,OAAOb,OAiBTiL,aAAc,SAAUpK,EAAM8K,EAAMC,GAClC,GAAI5L,KAAKoK,WAAWvJ,GA7IxB6D,EA8ImBrB,KAAK,cAAgBxC,EAAO,6BACtC,IAAI8D,EAAgB/C,eAAef,GAEtC,WAjJN6D,GAgJmBrB,KAAK,IAAMxC,EAAO,+DAGjC,OAAOb,MAAK6L,cAAAnL,MAALV,KAAsBQ,YAG/BsL,gBAAiB,SAAUjL,EAAM8K,EAAMC,GACrC,MAAK5L,MAAKoK,WAAWvJ,GAIdb,KAAK6L,cAAc7L,KAAMQ,YA3JpCkE,EAwJmBrB,KAAK,cAAgBxC,EAAO,6BAClCb,KAAKiL,aAAAvK,MAALV,KAAqBQ,aAKhCuL,gBAAiB,SAAUlL,GAMzB,MALKb,MAAKoK,WAAWvJ,IA/JzB6D,EAgKmBrB,KAAK,cAAgBxC,EAAO,2BAEpCb,MAAKoK,WAAWvJ,GAEhBb,MAGT6L,cAAe,SAAUhL,EAAMmL,EAAW9B,GACpC,gBAAoB8B,KAEtBA,GACE7L,GAAI6L,EACJ9B,SAAUA,IAGT8B,EAAUvC,WACbuC,EAAY,GAAI1C,GAAiB0C,IAEnChM,KAAKoK,WAAWvJ,GAAQmL,CAExB,KAAK,GAAI1B,KAAU0B,GAAUX,aAC3BrL,KAAKuL,WAAWjB,EAAQzJ,EAAMmL,EAAUX,SAASf,GAEnD,OAAOtK,OAGTiM,gBAAiB,SAAUC,GACzB,GAAIV,EAGJ,IAAI,SAAWU,EAAWrL,KAAM,CAC9B,GAAIsL,GAAenM,KAAKqK,QAAQrK,KAAKsK,QAAQ4B,EAAWrL,SACxD2K,GAAUW,EAAaD,EAAWnC,kBAElCyB,GAAUxL,KAAKoM,cAAcpM,KAAKqK,QAAQrK,KAAKsK,QAAQ4B,EAAWrL,MAAOqL,EAAWnC,aAEtF,OAAOyB,IAAWxL,KAAKqK,QAAQrK,KAAKsK,QAAQ+B,gBAAkBrM,KAAKqK,QAAQiC,GAAGD,gBAIhFD,cAAe,SAAUvI,EAAQ0I,GAC/B,GAAI,gBAAoBA,GAAY,CAClC,IAAK,GAAIvN,KAAKuN,GACZ1I,EAAS7D,KAAKoM,cAAcvI,EAAQ0I,EAAWvN,GAEjD,OAAO6E,GAGT,MAAO,gBAAoBA,GAASA,EAAOb,QAAQ,MAAOuJ,GAAc,IAU1EnC,YACEoC,UACE3C,eAAgB,SAAS5H,GACvB,MAAO,KAAKH,KAAKG,IAEnBiI,SAAU,GAEZuC,UACE9C,iBAAkB,SAASf,GACzB,MAAOA,GAAO1J,OAAS,GAEzB2K,eAAgB,SAAS5H,GACvB,MAAO,KAAKH,KAAKG,IAEnBiI,SAAU,KAEZF,MACEH,eAAgB,SAAS5H,EAAO+H,GPmb5B,GAAI0C,GAAOlM,UAAUtB,QAAU,GAAsByN,SAAjBnM,UAAU,MOnbaA,UAAA,GPqbvDoM,EAAYF,EOrbmBG,KAAAA,EAAAF,SAAAC,EAAO,IAAAA,EPubtCE,EAAYJ,EOvb+BK,KAAAA,EAAAJ,SAAAG,EAAO,EAAAA,EACpDpL,EAAQ8I,EAAYR,EACxB,KAAKtI,EACH,KAAM,IAAI6C,OAAM,mBAAqByF,EAAO,qBAE9C,KAAKtI,EAAMI,KAAKG,GACd,OAAO,CACT,IAAI,WAAa+H,IACV,SAASlI,KAAK+K,GAAQ,IAAK,CAC9B,GAAIG,GAAKpK,OAAOX,GACZgL,EAAWlC,KAAKC,IAAIF,EAAc+B,GAAO/B,EAAciC,GAC3D,IAAIjC,EAAckC,GAAMC,EACtB,OAAO,CAET,IAAIC,GAAQ,SAAAC,GAAO,MAAOpC,MAAKqC,MAAMD,EAAIpC,KAAKsC,IAAI,GAAIJ,IACtD,KAAKC,EAAMF,GAAME,EAAMH,IAASG,EAAML,IAAS,EAC7C,OAAO,EAGb,OAAO,GAET/D,iBACE,GAAI,SACJ+D,KAAM,SACNE,KAAM,UAER7C,SAAU,KAEZoD,SACEzD,eAAgB,SAAS5H,EAAOsG,GAC9B,MAAOA,GAAOzG,KAAKG,IAErB6G,gBAAiB,SACjBoB,SAAU,IAEZqD,WACE1D,eAAgB,SAAU5H,EAAOuL,GAC/B,MAAOvL,GAAM/C,QAAUsO,GAEzB1E,gBAAiB,UACjBoB,SAAU,IAEZuD,WACE5D,eAAgB,SAAU5H,EAAOuL,GAC/B,MAAOvL,GAAM/C,QAAUsO,GAEzB1E,gBAAiB,UACjBoB,SAAU,IAEZhL,QACE2K,eAAgB,SAAU5H,EAAOyL,EAAK1C,GACpC,MAAO/I,GAAM/C,QAAUwO,GAAOzL,EAAM/C,QAAU8L,GAEhDlC,iBAAkB,UAAW,WAC7BoB,SAAU,IAEZyD,UACEhE,iBAAkB,SAAUf,EAAQ4E,GAClC,MAAO5E,GAAO1J,QAAUsO,GAE1B1E,gBAAiB,UACjBoB,SAAU,IAEZ0D,UACEjE,iBAAkB,SAAUf,EAAQ4E,GAClC,MAAO5E,GAAO1J,QAAUsO,GAE1B1E,gBAAiB,UACjBoB,SAAU,IAEZ2D,OACElE,iBAAkB,SAAUf,EAAQ8E,EAAK1C,GACvC,MAAOpC,GAAO1J,QAAUwO,GAAO9E,EAAO1J,QAAU8L,GAElDlC,iBAAkB,UAAW,WAC7BoB,SAAU,IAEZwD,KACE9D,eAAgB,SAAU3H,EAAOuL,GAC/B,MAAOvL,IAASuL,GAElB1E,gBAAiB,SACjBoB,SAAU,IAEZc,KACEpB,eAAgB,SAAU3H,EAAOuL,GAC/B,MAAgBA,IAATvL,GAET6G,gBAAiB,SACjBoB,SAAU,IAEZW,OACEjB,eAAgB,SAAU3H,EAAOyL,EAAK1C,GACpC,MAAO/I,IAASyL,GAAgB1C,GAAT/I,GAEzB6G,iBAAkB,SAAU,UAC5BoB,SAAU,IAEZ4D,SACEjE,eAAgB,SAAU5H,EAAO8L,GAC/B,GAAIC,GAAa/N,EAAE8N,EACnB,OAAIC,GAAW9O,OACN+C,IAAU+L,EAAWC,MAErBhM,IAAU8L,GAErB7D,SAAU,MClVhB,IAAIgE,MAEAC,EAAc,QAAdA,GAAwBC,EAAWC,EAAWC,GAIhD,IAAK,GAHDC,MACAC,KAEKxP,EAAI,EAAGA,EAAIoP,EAAUlP,OAAQF,IAAK,CAGzC,IAAK,GAFDyP,IAAQ,EAEHC,EAAI,EAAGA,EAAIL,EAAUnP,OAAQwP,IACpC,GAAIN,EAAUpP,GAAG2P,OAAO9N,OAASwN,EAAUK,GAAGC,OAAO9N,KAAM,CACzD4N,GAAQ,CACR,OAGAA,EACFD,EAAKhI,KAAK4H,EAAUpP,IAEpBuP,EAAM/H,KAAK4H,EAAUpP,IAGzB,OACEwP,KAAMA,EACND,MAAOA,EACPK,QAAUN,KAAOH,EAAYE,EAAWD,GAAW,GAAMG,OAI7DL,GAAUW,MAERC,mBAAoB,WR0wBhB,GAAIC,GAAQ/O,IQzwBdA,MAAKqB,SAASiF,GAAG,iBAAkB,SAAApC,GAAS6K,EAAKC,iBAAiB9K,KAClElE,KAAKqB,SAASiF,GAAG,gBAAiB,8CAA+C,SAAApC,GAAS6K,EAAKE,eAAe/K,MAG1G,IAAUlE,KAAKgG,QAAQf,WAG3BjF,KAAKqB,SAASD,KAAK,aAAc,KAGnC+D,MAAO,WAGL,GAFAnF,KAAKkP,cAAgB,MAEjB,IAASlP,KAAKmP,kBAAoB,SAAWnP,KAAKgG,QAAQb,MAC5D,MAAO,KAET,KAAK,GAAInG,GAAI,EAAGA,EAAIgB,KAAKqH,OAAOnI,OAAQF,IAAK,CAC3C,GAAIoQ,GAAQpP,KAAKqH,OAAOrI,EACxB,KAAI,IAASoQ,EAAMD,kBAAoBC,EAAMD,iBAAiBjQ,OAAS,GAAK,mBAAuBkQ,GAAMpJ,QAAQqJ,UAC/GrP,KAAKkP,cAAgBE,EAAM/N,SACvB,UAAYrB,KAAKgG,QAAQb,OAC3B,MAIN,MAAI,QAASnF,KAAKkP,cACT,KAEFlP,KAAKkP,cAAc/J,SAG5BoC,WAAY,WAEVvH,KAAKqB,SAASsF,IAAI,cAKtBuH,EAAUoB,OAERC,UAAW,WAIT,GAHAvP,KAAKwP,WAGAxP,KAAKyP,IAAV,CAIA,GAAIC,GAAOvB,EAAYnO,KAAKmP,iBAAkBnP,KAAKyP,IAAIE,qBAGvD3P,MAAKyP,IAAIE,qBAAuB3P,KAAKmP,iBAGrCnP,KAAK4P,qBAGL5P,KAAK6P,sBAAsBH,GAG3B1P,KAAK8O,sBAGAY,EAAKlB,KAAKtP,SAAUwQ,EAAKnB,MAAMrP,QAAYc,KAAK8P,cACnD9P,KAAK8P,aAAc,EACnB9P,KAAK8O,wBAKTiB,kBAAmB,WAEjB,IAAI,IAAS/P,KAAKmP,iBAChB,QAIF,KAAK,GAFD9D,MAEKrM,EAAI,EAAGA,EAAIgB,KAAKmP,iBAAiBjQ,OAAQF,IAChDqM,EAAS7E,KAAKxG,KAAKmP,iBAAiBnQ,GAAGgR,cACtChQ,KAAKiQ,iBAAiBjQ,KAAKmP,iBAAiBnQ,GAAG2P,QAElD,OAAOtD,IAIT6E,SAAU,SAAUrP,GRwwBhB,GAAIsP,GAAQ3P,UAAUtB,QAAU,GAAsByN,SAAjBnM,UAAU,MQxwBeA,UAAA,GAAvCgL,EAAA2E,EAAA3E,QAASmD,EAAAwB,EAAAxB,OR4wB5ByB,EAAoBD,EQ5wBgBE,YAAAA,EAAA1D,SAAAyD,GAAc,EAAAA,CACxDpQ,MAAKwP,WACLxP,KAAKsQ,UAAUzP,GAAO2K,QAAAA,EAASmD,OAAAA,IAE3B0B,GACFrQ,KAAKuQ,eAITC,YAAa,SAAU3P,GR8wBnB,GAAI4P,GAAQjQ,UAAUtB,QAAU,GAAsByN,SAAjBnM,UAAU,MQ9wBkBA,UAAA,GAAvCgL,EAAAiF,EAAAjF,QAASmD,EAAA8B,EAAA9B,ORkxB/B+B,EAAoBD,EQlxBmBJ,YAAAA,EAAA1D,SAAA+D,GAAc,EAAAA,CAC3D1Q,MAAKwP,WACLxP,KAAK2Q,aAAa9P,GAAO2K,QAAAA,EAASmD,OAAAA,IAE9B0B,GACFrQ,KAAKuQ,eAITK,YAAa,SAAU/P,GRoxBnB,GAAIgQ,GAAQrQ,UAAUtB,QAAU,GAAsByN,SAAjBnM,UAAU,MQpxBCA,UAAA,GRsxB5CsQ,EAAoBD,EQtxBER,YAAAA,EAAA1D,SAAAmE,GAAc,EAAAA,CAC1C9Q,MAAKwP,WACLxP,KAAK+Q,aAAalQ,GAIdwP,GACFrQ,KAAK4P,sBAGTA,mBAAoB,WACd5P,KAAKgR,kBAAoBhR,KAAKiR,oBAAqB,IAASjR,KAAKmP,iBACnEnP,KAAKkR,gBACElR,KAAKmP,iBAAiBjQ,OAAS,EACtCc,KAAKuQ,cAELvQ,KAAKmR,eAGTtB,sBAAuB,SAAUH,GAC/B,GAAI,mBAAuB1P,MAAKgG,QAAQoL,uBAAxC,CAIA,GAAI,mBAAuBpR,MAAKgG,QAAQgK,aACtC,MAAKN,GAAKnB,MAAMrP,QAAUwQ,EAAKlB,KAAKtP,QAClCc,KAAKqR,sBAED,IAAMrR,KAAKyP,IAAI6B,eAAezJ,KAAK,iCAAiC3I,QACtEc,KAAKyP,IAAI6B,eACNC,OACCtR,EAAED,KAAKgG,QAAQJ,eACd4L,SAAS,iCAGTxR,KAAKyP,IAAI6B,eACbE,SAAS,UACT3J,KAAK,iCACL4J,KAAKzR,KAAKgG,QAAQgK,eAGhBhQ,KAAKyP,IAAI6B,eACbI,YAAY,UACZ7J,KAAK,iCACL8J,QAIL,KAAK,GAAI3S,GAAI,EAAGA,EAAI0Q,EAAKd,QAAQ1P,OAAQF,IACvCgB,KAAK+Q,aAAarB,EAAKd,QAAQ5P,GAAG2P,OAAO9N,KAE3C,KAAK7B,EAAI,EAAGA,EAAI0Q,EAAKnB,MAAMrP,OAAQF,IACjCgB,KAAKsQ,UAAUZ,EAAKnB,MAAMvP,GAAG2P,OAAO9N,MAAO2K,QAASkE,EAAKnB,MAAMvP,GAAGgR,aAAcrB,OAAQe,EAAKnB,MAAMvP,GAAG2P,QAExG,KAAK3P,EAAI,EAAGA,EAAI0Q,EAAKlB,KAAKtP,OAAQF,IAChCgB,KAAK2Q,aAAajB,EAAKlB,KAAKxP,GAAG2P,OAAO9N,MAAO2K,QAASkE,EAAKlB,KAAKxP,GAAGgR,aAAcrB,OAAQe,EAAKlB,KAAKxP,GAAG2P,WAI1G2B,UAAW,SAAUzP,EAAM+Q,GRmwBvB,GQnwBwBpG,GAADoG,EAACpG,QAASmD,EAAViD,EAAUjD,MACnC3O,MAAKqR,sBACLrR,KAAKyP,IAAI6B,eACNE,SAAS,UACTD,OACCtR,EAAED,KAAKgG,QAAQJ,eACd4L,SAAS,WAAa3Q,GACtB4Q,KAAKjG,GAAWxL,KAAKiQ,iBAAiBtB,MAI7CgC,aAAc,SAAU9P,EAAMgR,GRgwB1B,GQhwB2BrG,GAADqG,EAACrG,QAASmD,EAAVkD,EAAUlD,MACtC3O,MAAKyP,IAAI6B,eACNE,SAAS,UACT3J,KAAK,YAAchH,GACnB4Q,KAAKjG,GAAWxL,KAAKiQ,iBAAiBtB,KAG3CoC,aAAc,SAAUlQ,GACtBb,KAAKyP,IAAI6B,eACNI,YAAY,UACZ7J,KAAK,YAAchH,GACnB8Q,UAGL1B,iBAAkB,SAAU/D,GAC1B,GAAI4F,GAA+B5F,EAAWrL,KAAO,SAErD,OAAI,mBAAuBb,MAAKgG,QAAQ8L,GAC/BvO,OAAO2H,QAAQkB,cAAcpM,KAAKgG,QAAQ8L,GAA+B5F,EAAWnC,cAEtFxG,OAAO2H,QAAQe,gBAAgBC,IAGxCsD,SAAU,WAER,IAAIxP,KAAKyP,MAAO,IAAUzP,KAAKgG,QAAQf,UAAvC,CAGA,GAAIwK,KAGJzP,MAAKqB,SAASD,KAAKpB,KAAKgG,QAAQ1E,UAAY,KAAMtB,KAAK+R,QAIvDtC,EAAIuC,mBAAqBhS,KAAKiS,sBAG9BxC,EAAIyC,gBAAkB,eAAiBlS,KAAKgG,QAAQjB,SAAW,YAAc/E,KAAKgG,QAAQjB,SAAW/E,KAAK+R,QAC1GtC,EAAI6B,eAAiBrR,EAAED,KAAKgG,QAAQL,eAAevE,KAAK,KAAMqO,EAAIyC,iBAGlEzC,EAAIE,wBACJF,EAAI0C,8BAA+B,EAGnCnS,KAAKyP,IAAMA,IAIbwC,oBAAqB,WAEnB,GAAI,gBAAoBjS,MAAKgG,QAAQR,cAAgBvF,EAAED,KAAKgG,QAAQR,cAActG,OAChF,MAAOe,GAAED,KAAKgG,QAAQR,aAGxB,IAAI4M,GAAWpS,KAAKgG,QAAQR,aAAajF,KAAKP,KAAMA,KAGpD,OAAI,mBAAuBoS,IAAYA,EAASlT,OACvCkT,GAGJpS,KAAKgG,QAAQjB,UAAY/E,KAAKqB,SAASe,GAAG,UACtCpC,KAAKqB,SAGPrB,KAAKqB,SAAS6E,UAGvBmL,oBAAqB,WACnB,GAAIgB,EAGJ,IAAI,IAAMrS,KAAKyP,IAAI6B,eAAepL,SAAShH,OACzC,MAAOc,MAAKyP,IAAI6B,eAAepL,QAEjC,IAAI,gBAAoBlG,MAAKgG,QAAQN,gBAAiB,CACpD,GAAIzF,EAAED,KAAKgG,QAAQN,iBAAiBxG,OAClC,MAAOe,GAAED,KAAKgG,QAAQN,iBAAiB6L,OAAOvR,KAAKyP,IAAI6B,eA9R/D5M,GAgSqBrB,KAAK,yBAA2BrD,KAAKgG,QAAQN,gBAAkB,+BACrE,kBAAsB1F,MAAKgG,QAAQN,kBAC5C2M,EAAmBrS,KAAKgG,QAAQN,gBAAgBnF,KAAKP,KAAMA,MAE7D,IAAI,mBAAuBqS,IAAoBA,EAAiBnT,OAC9D,MAAOmT,GAAiBd,OAAOvR,KAAKyP,IAAI6B,eAE1C,IAAIgB,GAAQtS,KAAKqB,QAGjB,OAFIrB,MAAKgG,QAAQjB,WACfuN,EAAQA,EAAMpM,UACToM,EAAMC,MAAMvS,KAAKyP,IAAI6B,iBAG9BxC,mBAAoB,WRivBhB,GAAI0D,GAASxS,KQhvBXyS,EAAUzS,KAAK4H,cAGnB6K,GAAQ9L,IAAI,YACR3G,KAAK8P,YACP2C,EAAQnM,GAnTd5B,EAmT8BZ,gBAAgB9D,KAAKgG,QAAQX,oBAAqB,WAAY,WACpFmN,EAAK/I,aAGPgJ,EAAQnM,GAvTd5B,EAuT8BZ,gBAAgB9D,KAAKgG,QAAQZ,QAAS,WAAY,SAAAsN,GACxEF,EAAKG,eAAeD,MAK1BC,eAAgB,SAAUD,KAIpB,YAAY5Q,KAAK4Q,EAAM1I,OACnBhK,KAAKyP,KAAOzP,KAAKyP,IAAI0C,gCAAiCnS,KAAK4S,WAAW1T,QAAUc,KAAKgG,QAAQd,uBAGrGlF,KAAKyJ,YAGPtC,SAAU,WAERnH,KAAK8P,aAAc,EACnB9P,KAAK8O,qBAGD,mBAAuB9O,MAAKyP,MAIhCzP,KAAKyP,IAAI6B,eACNI,YAAY,UACZmB,WACAlB,SAGH3R,KAAKmR,cAGLnR,KAAKyP,IAAIE,wBACT3P,KAAKyP,IAAI0C,8BAA+B,IAG1C5K,WAAY,WACVvH,KAAKmH,WAED,mBAAuBnH,MAAKyP,KAC9BzP,KAAKyP,IAAI6B,eAAeK,eAEnB3R,MAAKyP,KAGdyB,cAAe,WACblR,KAAKyP,IAAI0C,8BAA+B,EACxCnS,KAAKyP,IAAIuC,mBAAmBN,YAAY1R,KAAKgG,QAAQV,YAAYkM,SAASxR,KAAKgG,QAAQT,eAEzFgL,YAAa,WACXvQ,KAAKyP,IAAI0C,8BAA+B,EACxCnS,KAAKyP,IAAIuC,mBAAmBN,YAAY1R,KAAKgG,QAAQT,cAAciM,SAASxR,KAAKgG,QAAQV,aAE3F6L,YAAa,WACXnR,KAAKyP,IAAIuC,mBAAmBN,YAAY1R,KAAKgG,QAAQT,cAAcmM,YAAY1R,KAAKgG,QAAQV,aC7WhG,IAAIwN,GAAc,SAAUC,EAAS9M,EAAYD,GAC/ChG,KAAKkH,UAAY,cACjBlH,KAAK+R,OANPrN,EAM6BjC,aAE3BzC,KAAKqB,SAAWpB,EAAE8S,GAClB/S,KAAKiG,WAAaA,EAClBjG,KAAKgG,QAAUA,EACfhG,KAAKkG,OAAS3C,OAAO2H,QAErBlL,KAAKqH,UACLrH,KAAKmP,iBAAmB,MAd1B6D,GAiBqBC,QAAS,KAAMC,UAAU,EAAMC,UAAU,EAE9DL,GAAYzT,WACV2P,iBAAkB,SAAU0D,GT2lCxB,GAAIU,GAASpT,ISzlCf,KAAI,IAAS0S,EAAM5S,QAAnB,CAIA,GAAIuT,GAAgBrT,KAAKsT,gBAAkBtT,KAAKqB,SAASwG,KAAK,+CAA+C0L,OAG7G,IAFAvT,KAAKsT,eAAiB,KACtBtT,KAAKqB,SAASwG,KAAK,oCAAoC2L,KAAK,YAAY,IACpEH,EAAcjR,GAAG,oBAArB,CAGA,GAAIqR,GAAUzT,KAAK0T,cAAchB,MAAAA,GAE7B,cAAee,EAAQE,UAAW,IAAU3T,KAAKoH,SAAS,YAK5DsL,EAAMkB,2BACNlB,EAAMmB,iBACF,YAAcJ,EAAQE,SACxBF,EAAQK,KAAK,WAAQV,EAAKW,QAAQV,SAIxCpE,eAAgB,SAASyD,GACvB1S,KAAKsT,eAAiBrT,EAAEyS,EAAM3L,SAKhCgN,QAAS,SAAUV,GACjB,IAAI,IAAUrT,KAAKoH,SAAS,UAA5B,CAGA,GAAIiM,EAAe,CACjB,GAAIW,GAAahU,KAAKqB,SAASwG,KAAK,oCAAoC2L,KAAK,YAAY,EACrF,KAAMQ,EAAW9U,SACnB8U,EAAa/T,EAAE,iEAAiEgU,SAASjU,KAAKqB,WAChG2S,EAAW5S,MACTP,KAAMwS,EAAcjS,KAAK,QACzBa,MAAOoR,EAAcjS,KAAK,WAI9BpB,KAAKqB,SAAS+D,QAAQnF,EAAEuJ,OAAOvJ,EAAEiU,MAAM,WAAYpU,SAAS,OAQ9D2J,SAAU,SAAUzD,GAClB,GAAIxF,UAAUtB,QAAU,IAAMe,EAAEgK,cAAcjE,GAAU,CA3E5DtB,EA4EmBjB,SAAS,2FT2lCpB,IAAI0Q,GAAa/U,OAAOmB,KS1lCEC,WAAvBwE,EAAAmP,EAAA,GAAOzM,EAAAyM,EAAA,GAAOzB,EAAAyB,EAAA,EACnBnO,IAAWhB,MAAAA,EAAO0C,MAAAA,EAAOgL,MAAAA,GAE3B,MAhFJM,GAgF0BhT,KAAK0T,aAAa1N,GAAS2N,UAGnDD,aAAc,WTgmCV,GAAIU,GAASpU,KAETqU,EAAQ7T,UAAUtB,QAAU,GAAsByN,SAAjBnM,UAAU,MSlmCHA,UAAA,GAAvBwE,EAAAqP,EAAArP,MAAO0C,EAAA2M,EAAA3M,MAAOgL,EAAA2B,EAAA3B,KACrC1S,MAAKsU,YAAc5B,EACfA,IACF1S,KAAKsU,YAAcrU,EAAEuJ,UAAWkJ,GAAQmB,eAAgB,WAtF9DnP,EAuFqBjB,SAAS,0GACtB2Q,EAAKjF,kBAAmB,MAG5BnP,KAAKmP,kBAAmB,EAGxBnP,KAAKoH,SAAS,YAGdpH,KAAKuU,gBAEL,IAAIC,GAAWxU,KAAKyU,iCAAiC,WACnD,MAAOxU,GAAEgE,IAAImQ,EAAK/M,OAAQ,SAAA+H,GACxB,MAAOA,GAAMsE,cAAchM,MAAAA,EAAO1C,MAAAA,QAIlC0P,EAAiC,WACnC,GAAIC,GAAI1U,EAAE2U,UAGV,QAFI,IAAUR,EAAKjF,kBACjBwF,EAAEE,SACGF,EAAEG,UAAUrB,UAGrB,OAAOxT,GAAE8U,KAAArU,MAAFT,EAAArB,mBAAU4V,IACdV,KAAO,WAAQM,EAAKhN,SAAS,aAC7B4N,KAAO,WACNZ,EAAKjF,kBAAmB,EACxBiF,EAAKjP,QACLiP,EAAKhN,SAAS,WAEf6N,OAAO,WAAQb,EAAKhN,SAAS,eAC7B8N,KAAOR,EAAgCA,IAO5CS,QAAS,SAAUnP,GACjB,GAAIxF,UAAUtB,QAAU,IAAMe,EAAEgK,cAAcjE,GAAU,CAhI5DtB,EAiImBjB,SAAS,0FTwmCpB,IAAI2R,GAAchW,OAAOmB,KSvmCNC,WAAhBwE,EAAAoQ,EAAA,GAAO1N,EAAA0N,EAAA,EACZpP,IAAWhB,MAAAA,EAAO0C,MAAAA,GAEpB,MArIJsL,GAqI0BhT,KAAK2H,UAAU3B,GAAS2N,UAMhDhM,UAAW,WT4mCP,GAAI0N,GAASrV,KAETsV,EAAQ9U,UAAUtB,QAAU,GAAsByN,SAAjBnM,UAAU,MS9mCbA,UAAA,GAAhBwE,EAAAsQ,EAAAtQ,MAAO0C,EAAA4N,EAAA5N,KAC3B1H,MAAKuU,gBAEL,IAAIC,GAAWxU,KAAKyU,iCAAiC,WACnD,MAAOxU,GAAEgE,IAAIoR,EAAKhO,OAAQ,SAAA+H,GACxB,MAAOA,GAAMzH,WAAW3C,MAAAA,EAAO0C,MAAAA,OAGnC,OAAOzH,GAAE8U,KAAArU,MAAFT,EAAArB,mBAAU4V,KAGnBD,eAAgB,WACd,MAAOvU,MAAK+F,mBAAmBwP,eAGjCA,YAAa,WTmnCT,GAAIC,GAASxV,KSlnCXyV,EAAYzV,KAAKqH,MAwBrB,OAtBArH,MAAKqH,UACLrH,KAAK0V,oBAEL1V,KAAKyU,iCAAiC,WACpCe,EAAKnU,SACJwG,KAAK2N,EAAKxP,QAAQpB,QAClB+Q,IAAIH,EAAKxP,QAAQnB,UACjB+Q,KAAK,SAACC,EAAG9C,GACR,GAAI+C,GAAgB,GAAIvS,QAAO2H,QAAQ6K,QAAQhD,KAASyC,EAGnD,kBAAmBM,EAAc5O,WAAa,yBAA2B4O,EAAc5O,YAAe,IAAS4O,EAAc9P,QAAQnB,UACpI,mBAAuB2Q,GAAKE,iBAAiBI,EAAc5O,UAAY,IAAM4O,EAAc/D,UAC7FyD,EAAKE,iBAAiBI,EAAc5O,UAAY,IAAM4O,EAAc/D,QAAU+D,EAC9EN,EAAKnO,OAAOb,KAAKsP,MAIvB7V,EAAEwV,GAAWE,IAAIH,EAAKnO,QAAQuO,KAAK,SAACC,EAAGzG,GACrCA,EAAMhI,SAAS,aAGZpH,MAUTyU,iCAAkC,SAAUtU,GAC1C,GAAI6V,GAAsBhW,KAAK+F,gBAC/B/F,MAAK+F,iBAAmB,WAAc,MAAO/F,MAC7C,IAAIyE,GAAStE,GAEb,OADAH,MAAK+F,iBAAmBiQ,EACjBvR,GAMT2C,SAAU,SAAUxG,GAClB,MAAOZ,MAAKoF,QAAQ,QAAUxE,ICpMlC,IAAIqV,GAAoB,SAAUC,EAAcrV,EAAMkJ,EAAcG,EAAUiM,GAC5E,IAAK,eAAerU,KAAKoU,EAAahP,WACpC,KAAM,IAAI3C,OAAM,yDAElB,IAAI6R,GAAgB7S,OAAO2H,QAAQmL,mBAAmBjM,WAAWvJ,GAC7DmL,EAAY,GAAI1C,GAAiB8M,EAErCnW,GAAEuJ,OAAOxJ,MACPgM,UAAWA,EACXnL,KAAMA,EACNkJ,aAAcA,EACdG,SAAUA,GAAYgM,EAAalQ,QAAQnF,EAAO,aAAemL,EAAU9B,SAC3EiM,iBAAiB,IAASA,IAE5BnW,KAAKsW,mBAAmBJ,EAAalQ,UAGnCuQ,EAAa,SAASxT,GACxB,GAAIyT,GAAMzT,EAAI,GAAGI,aACjB,OAAOqT,GAAMzT,EAAIzD,MAAM,GAGzB2W,GAAkB5W,WAChBoK,SAAU,SAASxH,EAAOwU,GACxB,GAAInW,GAAON,KAAK0W,gBAAgBpX,MAAM,EAGtC,OAFAgB,GAAKG,QAAQwB,GACb3B,EAAKkG,KAAKiQ,GACHzW,KAAKgM,UAAUvC,SAAS/I,MAAMV,KAAKgM,UAAW1L,IAGvDgW,mBAAoB,SAAStQ,GV2zCzB,GAAI2Q,GAAS3W,IU1zCfA,MAAK0W,gBAAkB1W,KAAKgM,UAAUlC,kBAAkB9J,KAAK+J,aAAc,SAAAV,GACzE,MAAOrD,GAAQ2Q,EAAK9V,KAAO0V,EAAWlN,OChC5C,IAAI5D,GAAe,SAAU2J,EAAOnJ,EAAYD,EAAS4Q,GACvD5W,KAAKkH,UAAY,eACjBlH,KAAK+R,OAPPrN,EAO6BjC,aAE3BzC,KAAKqB,SAAWpB,EAAEmP,GAGd,mBAAuBwH,KACzB5W,KAAKkG,OAAS0Q,GAGhB5W,KAAKgG,QAAUA,EACfhG,KAAKiG,WAAaA,EAGlBjG,KAAK6W,eACL7W,KAAK8W,qBACL9W,KAAKmP,oBAGLnP,KAAK+W,oBAzBPC,GA4BqB/D,QAAS,KAAMC,UAAU,EAAMC,UAAU,EAE9D1N,GAAapG,WAKXoK,SAAU,SAAUzD,GACdxF,UAAUtB,QAAU,IAAMe,EAAEgK,cAAcjE,KApClDtB,EAqCmBjB,SAAS,6FACtBuC,GAAWA,QAAAA,GAEb,IAAIyN,GAAUzT,KAAK0T,aAAa1N,EAChC,KAAKyN,EACH,OAAO,CACT,QAAQA,EAAQE,SACd,IAAK,UAAW,MAAO,KACvB,KAAK,WAAY,OAAO,CACxB,KAAK,WAAY,MAAO3T,MAAKmP,mBAOjCuE,aAAc,WXq2CV,GAAIuD,GAASjX,KAETkX,EAAQ1W,UAAUtB,QAAU,GAAsByN,SAAjBnM,UAAU,MWv2CTA,UAAA,GAAjBkH,EAAAwP,EAAAxP,MAAO1C,EAAAkS,EAAAlS,KAG9B,OADAhF,MAAKmX,sBACDnS,GAAUhF,KAAKoX,WAAWpS,IAG9BhF,KAAKiC,MAAQjC,KAAK4S,WAGlB5S,KAAKoH,SAAS,YAEPpH,KAAK2H,WAAWD,MAAAA,EAAOzF,MAAOjC,KAAKiC,MAAOoV,YAAY,IAC1DpC,OAAO,WAAQgC,EAAK1H,cACpBuE,KAAK,WAAUmD,EAAK7P,SAAS,aAC7B4N,KAAK,WAAUiC,EAAK7P,SAAS,WAC7B6N,OAAO,WAAQgC,EAAK7P,SAAS,gBAZhC,QAeF4J,eAAgB,WACd,MAAO,KAAMhR,KAAK6W,YAAY3X,QAIhC+R,gBAAiB,SAAUhP,GAMzB,MALI,mBAAuBA,KACzBA,EAAQjC,KAAK4S,YAIV3Q,EAAM/C,QAAWc,KAAKsX,eAAiB,mBAAuBtX,MAAKgG,QAAQuR,iBAGzE,GAFE,GAKXH,WAAY,SAAUpS,GACpB,MAAI/E,GAAElB,QAAQiB,KAAKgG,QAAQhB,OAClB,KAAO/E,EAAEuX,QAAQxS,EAAOhF,KAAKgG,QAAQhB,OACvChF,KAAKgG,QAAQhB,QAAUA,GAOhCmQ,QAAS,SAAUnP,GACjB,GAAIxF,UAAUtB,QAAU,IAAMe,EAAEgK,cAAcjE,GAAU,CAnG5DtB,EAoGmBjB,SAAS,2FX62CpB,IAAIgU,GAAcrY,OAAOmB,KW52CNC,WAAhBkH,EAAA+P,EAAA,GAAOxV,EAAAwV,EAAA,EACZzR,IAAW0B,MAAAA,EAAOzF,MAAAA,GAEpB,GAAIwR,GAAUzT,KAAK2H,UAAU3B,EAC7B,OAAKyN,GAzGTuD,EA2GyBvD,EAAQE,UADpB,GASXhM,UAAW,WXi3CP,GAAI+P,GAAS1X,KAET2X,EAASnX,UAAUtB,QAAU,GAAsByN,SAAjBnM,UAAU,MWn3CaA,UAAA,GXq3CzDoX,EAAeD,EWr3CDjQ,MAAAA,EAAAiF,SAAAiL,GAAQ,EAAAA,EAAO3V,EAAA0V,EAAA1V,MAAO+C,EAAA2S,EAAA3S,MAAOqS,EAAAM,EAAAN,UAKjD,IAHKA,GACHrX,KAAKmX,sBAEHnS,GAAUhF,KAAKoX,WAAWpS,GAA9B,CAMA,GAHAhF,KAAKmP,kBAAmB,GAGnBnP,KAAKgR,iBACR,MAAO/Q,GAAE8U,MAMX,KAHI,mBAAuB9S,IAAS,OAASA,KAC3CA,EAAQjC,KAAK4S,aAEV5S,KAAKiR,gBAAgBhP,KAAU,IAASyF,EAC3C,MAAOzH,GAAE8U,MAEX,IAAI8C,GAAqB7X,KAAK8X,yBAC1BtD,IAWJ,OAVAvU,GAAE2V,KAAKiC,EAAoB,SAAChC,EAAGgB,GAG7B,GAAIpD,GAAUxT,EAAE8U,KAAArU,MAAFT,EAAArB,mBACTqB,EAAEgE,IAAI4S,EAAa,SAAA3K,GXq3CpB,MWr3CkCwL,GAAKK,oBAAoB9V,EAAOiK,MAGtE,OADAsI,GAAShO,KAAKiN,GACU,aAApBA,EAAQE,SACH,EADT,SAGK1T,EAAE8U,KAAKrU,MAAMT,EAAGuU,KAIzBuD,oBAAqB,SAAS9V,EAAOiK,GXq3CjC,GAAI8L,GAAUhY,KWp3CZyE,EAASyH,EAAWzC,SAASxH,EAAOjC,KAKxC,QAHI,IAAUyE,IACZA,EAASxE,EAAE2U,WAAWC,UAEjB5U,EAAE8U,KAAKtQ,GAAQuQ,KAAK,SAAAhF,IACrB,IAASgI,EAAK7I,mBAChB6I,EAAK7I,qBACP6I,EAAK7I,iBAAiB3I,MACpBmI,OAAQzC,EACR8D,aAAc,gBAAoBA,IAAgBA,OAMxD4C,SAAU,WACR,GAAI3Q,EAWJ,OAPEA,GADE,kBAAsBjC,MAAKgG,QAAQ/D,MAC7BjC,KAAKgG,QAAQ/D,MAAMjC,MACpB,mBAAuBA,MAAKgG,QAAQ/D,MACnCjC,KAAKgG,QAAQ/D,MAEbjC,KAAKqB,SAAS4M,MAGpB,mBAAuBhM,IAAS,OAASA,EACpC,GAEFjC,KAAKiY,kBAAkBhW,IAKhCkV,mBAAoB,WAClB,MAAOnX,MAAK+F,mBAAmBgR,oBAWjCmB,cAAe,SAAUrX,EAAMkJ,EAAcG,EAAUiM,GAErD,GAAI5S,OAAO2H,QAAQmL,mBAAmBjM,WAAWvJ,GAAO,CACtD,GAAIqL,GAAa,GAAI+J,GAAkBjW,KAAMa,EAAMkJ,EAAcG,EAAUiM,EAGvE,eAAgBnW,KAAK8W,kBAAkB5K,EAAWrL,OACpDb,KAAKmY,iBAAiBjM,EAAWrL,MAEnCb,KAAK6W,YAAYrQ,KAAK0F,GACtBlM,KAAK8W,kBAAkB5K,EAAWrL,MAAQqL,EAG5C,MAAOlM,OAITmY,iBAAkB,SAAUtX,GAC1B,IAAK,GAAI7B,GAAI,EAAGA,EAAIgB,KAAK6W,YAAY3X,OAAQF,IAC3C,GAAI6B,IAASb,KAAK6W,YAAY7X,GAAG6B,KAAM,CACrCb,KAAK6W,YAAYjQ,OAAO5H,EAAG,EAC3B,OAGJ,aADOgB,MAAK8W,kBAAkBjW,GACvBb,MAIToY,iBAAkB,SAAUvX,EAAM0L,EAAYrC,GAC5C,MAAOlK,MAAKmY,iBAAiBtX,GAC1BqX,cAAcrX,EAAM0L,EAAYrC,IAOrC6M,iBAAkB,WAKhB,IAAK,GAJDF,MACAC,KAGK9X,EAAI,EAAGA,EAAIgB,KAAK6W,YAAY3X,OAAQF,KACvC,IAAUgB,KAAK6W,YAAY7X,GAAGmX,kBAChCU,EAAYrQ,KAAKxG,KAAK6W,YAAY7X,IAClC8X,EAAkB9W,KAAK6W,YAAY7X,GAAG6B,MAAQb,KAAK6W,YAAY7X,GAGnEgB,MAAK6W,YAAcA,EACnB7W,KAAK8W,kBAAoBA,CAGzB,KAAK,GAAIjW,KAAQb,MAAKgG,QACpBhG,KAAKkY,cAAcrX,EAAMb,KAAKgG,QAAQnF,GAAO8L,QAAW,EAG1D,OAAO3M,MAAKqY,yBAKdA,sBAAuB,YAEjBrY,KAAKqB,SAASiX,SAAS,aAAetY,KAAKqB,SAASD,KAAK,cAC3DpB,KAAKkY,cAAc,YAAY,EAAMvL,QAAW,GAG9C,gBAAoB3M,MAAKqB,SAASD,KAAK,YACzCpB,KAAKkY,cAAc,UAAWlY,KAAKqB,SAASD,KAAK,WAAYuL,QAAW,GAGtE,mBAAuB3M,MAAKqB,SAASD,KAAK,QAAU,mBAAuBpB,MAAKqB,SAASD,KAAK,OAChGpB,KAAKkY,cAAc,SAAUlY,KAAKqB,SAASD,KAAK,OAAQpB,KAAKqB,SAASD,KAAK,QAASuL,QAAW,GAGxF,mBAAuB3M,MAAKqB,SAASD,KAAK,OACjDpB,KAAKkY,cAAc,MAAOlY,KAAKqB,SAASD,KAAK,OAAQuL,QAAW,GAGzD,mBAAuB3M,MAAKqB,SAASD,KAAK,QACjDpB,KAAKkY,cAAc,MAAOlY,KAAKqB,SAASD,KAAK,OAAQuL,QAAW,GAI9D,mBAAuB3M,MAAKqB,SAASD,KAAK,cAAgB,mBAAuBpB,MAAKqB,SAASD,KAAK,aACtGpB,KAAKkY,cAAc,UAAWlY,KAAKqB,SAASD,KAAK,aAAcpB,KAAKqB,SAASD,KAAK,cAAeuL,QAAW,GAGrG,mBAAuB3M,MAAKqB,SAASD,KAAK,aACjDpB,KAAKkY,cAAc,YAAalY,KAAKqB,SAASD,KAAK,aAAcuL,QAAW,GAGrE,mBAAuB3M,MAAKqB,SAASD,KAAK,cACjDpB,KAAKkY,cAAc,YAAalY,KAAKqB,SAASD,KAAK,aAAcuL,QAAW,EAI9E,IAAI3C,GAAOhK,KAAKqB,SAASD,KAAK,OAE9B,OAAI,mBAAuB4I,GAClBhK,KAGL,WAAagK,EACRhK,KAAKkY,cAAc,QAAS,UACjCrL,KAAM7M,KAAKqB,SAASD,KAAK,QACzB2L,KAAM/M,KAAKqB,SAASD,KAAK,QAAUpB,KAAKqB,SAASD,KAAK,WACpDuL,QAAW,GAEN,uBAAuB7K,KAAKkI,GAC9BhK,KAAKkY,cAAc,OAAQlO,EAAM2C,QAAW,GAE9C3M,MAKTsX,YAAa,WACX,MAAI,mBAAuBtX,MAAK8W,kBAAkBrK,UACzC,GAEF,IAAUzM,KAAK8W,kBAAkBrK,SAAS1C,cAKnD3C,SAAU,SAAUxG,GAClB,MAAOZ,MAAKoF,QAAQ,SAAWxE,IAOjCqX,kBAAmB,SAAUhW,GAU3B,OATI,IAASjC,KAAKgG,QAAQuS,WAhV9B7T,EAiVmBjB,SAAS,2FAEpB,WAAazD,KAAKgG,QAAQwS,aAC5BvW,EAAQA,EAAMe,QAAQ,UAAW,OAE/B,SAAYhD,KAAKgG,QAAQwS,YAAgB,WAAaxY,KAAKgG,QAAQwS,aAAgB,IAASxY,KAAKgG,QAAQuS,aAC3GtW,EAvVNyC,EAuV2Bd,WAAW3B,IAE3BA,GAMT6V,uBAAwB,WACtB,IAAI,IAAU9X,KAAKgG,QAAQlB,gBACzB,OAAQ9E,KAAK6W,YAMf,KAAK,GAJDgB,MACAY,KAGKzZ,EAAI,EAAGA,EAAIgB,KAAK6W,YAAY3X,OAAQF,IAAK,CAChD,GAAI0Z,GAAI1Y,KAAK6W,YAAY7X,GAAGkL,QACvBuO,GAAMC,IACTb,EAAmBrR,KAAKiS,EAAMC,OAChCD,EAAMC,GAAGlS,KAAKxG,KAAK6W,YAAY7X,IAKjC,MAFA6Y,GAAmBc,KAAK,SAAUC,EAAGC,GAAK,MAAOA,GAAE,GAAG3O,SAAW0O,EAAE,GAAG1O,WAE/D2N,GAhXX,IAAAiB,GAAArT,ECEIsT,EAAkB,WACpB/Y,KAAKkH,UAAY,uBAGnB6R,GAAgB1Z,WAEd2Z,WAAY,SAAU3X,GAGpB,MAFArB,MAAKiZ,UAAUzS,KAAKnF,GAEbrB,MAITmX,mBAAoB,WAClB,GAAI+B,EAKJ,IAHAlZ,KAAK6W,eAGD7W,KAAKqB,SAASe,GAAG,UAGnB,MAFApC,MAAK+F,mBAAmBgR,mBAEjB/W,IAIT,KAAK,GAAIhB,GAAI,EAAGA,EAAIgB,KAAKiZ,UAAU/Z,OAAQF,IAGzC,GAAKiB,EAAE,QAAQkZ,IAAInZ,KAAKiZ,UAAUja,IAAIE,OAAtC,CAKAga,EAAmBlZ,KAAKiZ,UAAUja,GAAGoa,KAAK,wBAAwBjC,qBAAqBN,WAEvF,KAAK,GAAInI,GAAI,EAAGA,EAAIwK,EAAiBha,OAAQwP,IAC3C1O,KAAKkY,cAAcgB,EAAiBxK,GAAG7N,KAAMqY,EAAiBxK,GAAG3E,aAAcmP,EAAiBxK,GAAGxE,SAAUgP,EAAiBxK,GAAGyH,qBAPjInW,MAAKiZ,UAAUrS,OAAO5H,EAAG,EAU7B,OAAOgB,OAIT4S,SAAU,WAER,GAAI,kBAAsB5S,MAAKgG,QAAQ/D,MACrCA,MAAQjC,KAAKgG,QAAQ/D,MAAMjC,UACxB,IAAI,mBAAuBA,MAAKgG,QAAQ/D,MAC3C,MAAOjC,MAAKgG,QAAQ/D,KAGtB,IAAIjC,KAAKqB,SAASe,GAAG,qBACnB,MAAOpC,MAAK4H,eAAeyR,OAAO,YAAYpL,OAAS,EAGzD,IAAIjO,KAAKqB,SAASe,GAAG,wBAAyB,CAC5C,GAAIwG,KAMJ,OAJA5I,MAAK4H,eAAeyR,OAAO,YAAYzD,KAAK,WAC1ChN,EAAOpC,KAAKvG,EAAED,MAAMiO,SAGfrF,EAIT,MAAI5I,MAAKqB,SAASe,GAAG,WAAa,OAASpC,KAAKqB,SAAS4M,SAIlDjO,KAAKqB,SAAS4M,OAGvBqL,MAAO,WAGL,MAFAtZ,MAAKiZ,WAAajZ,KAAKqB,UAEhBrB,MCxEX,IAAIuZ,GAAiB,SAAUxG,EAAS/M,EAAS4Q,GAC/C5W,KAAKqB,SAAWpB,EAAE8S,EAGlB,IAAIyG,GAA2BxZ,KAAKqB,SAAS+X,KAAK,UAClD,IAAII,EAQF,MALI,mBAAuB5C,IAAuB4C,EAAyBtT,SAAW3C,OAAO2H,UAC3FsO,EAAyBtT,OAAS0Q,EAClC4C,EAAyBrT,cAAcqT,EAAyBxT,UAG3DwT,CAIT,KAAKxZ,KAAKqB,SAASnC,OACjB,KAAM,IAAIqF,OAAM,gDAElB,IAAI,mBAAuBqS,IAAuB,gBAAkBA,EAAoB1P,UACtF,KAAM,IAAI3C,OAAM,iDAGlB,OADAvE,MAAKkG,OAAS0Q,GAAuBrT,OAAO2H,QACrClL,KAAKuK,KAAKvE,GAGnBuT,GAAela,WACbkL,KAAM,SAAUvE,GASd,MARAhG,MAAKkH,UAAY,UACjBlH,KAAKyZ,YAAc,QACnBzZ,KAAK+R,OAtCTrN,EAsC+BjC,aAG3BzC,KAAKmG,cAAcH,GAGfhG,KAAKqB,SAASe,GAAG,SA5CzBsC,EA4CkDxC,UAAUlC,KAAKqB,SAAUrB,KAAKgG,QAAQ1E,UAAW,cAAgBtB,KAAKqB,SAASe,GAAGpC,KAAKgG,QAAQpB,QACpI5E,KAAK0Z,KAAK,eAGZ1Z,KAAK2Z,aAAe3Z,KAAK4Z,iBAAmB5Z,KAAK0Z,KAAK,iBAG/DC,WAAY,WACV,MAAO3Z,MAAMqB,SAASe,GAAG,4CAAgDpC,KAAKqB,SAASe,GAAG,WAAa,mBAAuBpC,MAAKqB,SAASD,KAAK,aAKnJwY,eAAgB,WbmxDZ,GalxDE/Y,GAEAgZ,EbgxDEC,EAAU9Z,IarwDhB,IARIA,KAAKgG,QAAQjB,WAER,mBAAuB/E,MAAKqB,SAASD,KAAK,SAAWpB,KAAKqB,SAASD,KAAK,QAAQlC,OACvFc,KAAKgG,QAAQjB,SAAWlE,EAAOb,KAAKqB,SAASD,KAAK,QAC3C,mBAAuBpB,MAAKqB,SAASD,KAAK,OAASpB,KAAKqB,SAASD,KAAK,MAAMlC,SACnFc,KAAKgG,QAAQjB,SAAW/E,KAAKqB,SAASD,KAAK,QAGzCpB,KAAKqB,SAASe,GAAG,WAAa,mBAAuBpC,MAAKqB,SAASD,KAAK,YAE1E,MADApB,MAAKgG,QAAQjB,SAAW/E,KAAKgG,QAAQjB,UAAY/E,KAAK+R,OAC/C/R,KAAK0Z,KAAK,uBAGZ,KAAK1Z,KAAKgG,QAAQjB,SAEvB,MA9ENL,GA6EmBrB,KAAK,wHAAyHrD,KAAKqB,UACzIrB,IAITA,MAAKgG,QAAQjB,SAAW/E,KAAKgG,QAAQjB,SAAS/B,QAAQ,yBAA0B;AAG5E,mBAAuBnC,IACzBZ,EAAE,eAAiBY,EAAO,MAAM+U,KAAK,SAAC5W,EAAG+a,GACnC9Z,EAAE8Z,GAAO3X,GAAG,4CACdnC,EAAE8Z,GAAO3Y,KAAK0Y,EAAK9T,QAAQ1E,UAAY,WAAYwY,EAAK9T,QAAQjB,WAMtE,KAAK,GADDiV,GAAqBha,KAAK4H,eACrB5I,EAAI,EAAGA,EAAIgb,EAAmB9a,OAAQF,IAE7C,GADA6a,EAA0B5Z,EAAE+Z,EAAmBC,IAAIjb,IAAIoa,KAAK,WACxD,mBAAuBS,GAAyB,CAE7C7Z,KAAKqB,SAAS+X,KAAK,yBACtBS,EAAwBb,WAAWhZ,KAAKqB,SAG1C,OAQJ,MAFArB,MAAK0Z,KAAK,gBAAgB,GAEnBG,GAA2B7Z,KAAK0Z,KAAK,yBAI9CA,KAAM,SAAU1P,EAAMkQ,GACpB,GAAIC,EAEJ,QAAQnQ,GACN,IAAK,cACHmQ,EAAkBla,EAAEuJ,OAClB,GAAIsJ,GAAY9S,KAAKqB,SAAUrB,KAAKiG,WAAYjG,KAAKgG,SACrDzC,OAAO6W,eACP7E,aACF,MACF,KAAK,eACH4E,EAAkBla,EAAEuJ,OAClB,GA9HVsP,GA8H2B9Y,KAAKqB,SAAUrB,KAAKiG,WAAYjG,KAAKgG,QAAShG,KAAKkG,QACpE3C,OAAO6W,cAET,MACF,KAAK,uBACHD,EAAkBla,EAAEuJ,OAClB,GApIVsP,GAoI2B9Y,KAAKqB,SAAUrB,KAAKiG,WAAYjG,KAAKgG,QAAShG,KAAKkG,QACpE,GAAI6S,GACJxV,OAAO6W,eACPd,OACF,MACF,SACE,KAAM,IAAI/U,OAAMyF,EAAO,mCAM3B,MAHIhK,MAAKgG,QAAQjB,UA7IrBL,EA8ImBrC,QAAQrC,KAAKqB,SAAUrB,KAAKgG,QAAQ1E,UAAW,WAAYtB,KAAKgG,QAAQjB,UAEnF,mBAAuBmV,IACzBla,KAAKqB,SAAS+X,KAAK,uBAAwBe,GAEpCA,IAITna,KAAKqB,SAAS+X,KAAK,UAAWe,GAG9BA,EAAgBrL,qBAChBqL,EAAgB/S,SAAS,QAElB+S,IClJX,IAAIE,GAAUpa,EAAEE,GAAGma,OAAOtW,MAAM,IAChC,IAAIiE,SAASoS,EAAQ,KAAO,GAAKpS,SAASoS,EAAQ,IAAM,EACtD,KAAM,6EAEHA,GAAQE,SAfb7V,EAgBerB,KAAK,4FAGpB,IAAI6H,GAAUjL,EAAEuJ,OAAO,GAAI3D,IACvBxE,SAAUpB,EAAEua,UACZzU,iBAAkB,KAClBI,cAAe,KACf4P,QAASwD,EACTkB,QAAS,SAKbxa,GAAEuJ,OA7BFsP,EA6BsBzZ,UAAW6O,EAAUoB,MAAOzJ,EAAgBxG,WAClEY,EAAEuJ,OAAOsJ,EAAYzT,UAAW6O,EAAUW,KAAMhJ,EAAgBxG,WAEhEY,EAAEuJ,OAAO+P,EAAela,UAAWwG,EAAgBxG,WAInDY,EAAEE,GAAGL,QAAUG,EAAEE,GAAGua,KAAO,SAAU1U,GACnC,GAAIhG,KAAKd,OAAS,EAAG,CACnB,GAAIyb,KAMJ,OAJA3a,MAAK4V,KAAK,WACR+E,EAAUnU,KAAKvG,EAAED,MAAMF,QAAQkG,MAG1B2U,EAIT,MAAK1a,GAAED,MAAMd,OAMN,GAAIqa,GAAevZ,KAAMgG,OAtDlCtB,GAiDiBrB,KAAK,kDAUlB,mBAAuBE,QAAO6W,gBAChC7W,OAAO6W,kBAITlP,EAAQlF,QAAU/F,EAAEuJ,OAhEpB9E,EAgEwCN,aAAaO,GAAkBpB,OAAOqX,eAC9ErX,OAAOqX,cAAgB1P,EAAQlF,QAG/BzC,OAAO2H,QAAU3H,OAAOmX,KAAOxP,EAC/B3H,OAAOsX,aArEPnW,CAwEA,IAAIoW,GAAWvX,OAAO2H,QAAQmL,mBAAqB,GAAIlM,GAAyB5G,OAAOqX,cAAcxQ,WAAY7G,OAAOqX,cAAcG,KACtIxX,QAAO+F,oBACPrJ,EAAE2V,KAAK,yHAAyH5R,MAAM,KAAM,SAAUhF,EAAGgc,GACvJzX,OAAO2H,QAAQ8P,GAAU/a,EAAEgb,MAAMH,EAAUE,GAC3CzX,OAAO+F,iBAAiB0R,GAAU,Wd05D9B,GAAIE,Ecx5DN,OA9EJxW,GA6EiBjB,SAAA,yBAAkCuX,EAAA,yEAA+EA,EAAA,WACvHE,EAAA3X,OAAO2H,SAAQ8P,GAAAta,MAAAwa,EAAW1a,cAMrC+C,OAAO2H,QAAQiQ,GAAKjN,EACpB3K,OAAO2K,WACL0C,YAAa,SAAU6F,EAAU5V,EAAMua,GACrC,GAAI/K,IAAc,IAAS+K,CAE3B,OAzFJ1W,GAwFiBjB,SAAA,qJACNgT,EAAS7F,YAAY/P,GAAOwP,YAAAA,KAErCN,kBAAmB,SAAU0G,GAE3B,MA7FJ/R,GA4FiBjB,SAAA,yFACNgT,EAAS1G,sBAGpB9P,EAAE2V,KAAK,uBAAuB5R,MAAM,KAAM,SAAUhF,EAAGgc,GACrDzX,OAAO2K,UAAU8M,GAAU,SAAUvE,EAAU5V,EAAM2K,EAASmD,EAAQyM,GACpE,GAAI/K,IAAc,IAAS+K,CAE3B,OApGJ1W,GAmGiBjB,SAAA,4CAAqDuX,EAAA,iGAC3DvE,EAASuE,GAAQna,GAAO2K,QAAAA,EAASmD,OAAAA,EAAQ0B,YAAAA,OAMhD,WAAWvO,KAAKuZ,UAAUC,YAC5Brb,EAAEua,UAAUlU,GAAG,SAAU,SAAU,SAAApC,GACjCjE,EAAEiE,EAAI6C,QAAQ3B,QAAQ,YAMtB,IAAU7B,OAAOqX,cAAcW,UACjCtb,EAAE,WAEIA,EAAE,2BAA2Bf,QAC/Be,EAAE,2BAA2BH,WZjHnC,IAAIa,GAAIV,MACJub,EAAa,WANjB9W,EAOejB,SAAS,iHAgBpB1C,EAAc,UASlBd,GAAEwb,OAAS,SAAU5a,EAAM6a,GACzB,GAAItb,EAOJ,IANAob,IACI,gBAAoBhb,WAAU,IAAM,kBAAsBA,WAAU,KACtEJ,EAAUI,UAAU,GACpBkb,EAAWlb,UAAU,IAGnB,kBAAsBkb,GACxB,KAAM,IAAInX,OAAM,mBAElBhB,QAAO2H,QAAQ5E,GAAG1F,EAAUC,GAAOX,EAAMwb,EAAUtb,KAGrDH,EAAEyG,SAAW,SAAU+P,EAAU5V,EAAMV,GAErC,GADAqb,MACM/E,YAhDRqC,IAgD+CrC,YAAoB3D,IAC/D,KAAM,IAAIvO,OAAM,6BAElB,IAAI,gBAAoB1D,IAAQ,kBAAsBV,GACpD,KAAM,IAAIoE,OAAM,mBAElBkS,GAASnQ,GAAG1F,EAAUC,GAAOX,EAAMC,KAGrCF,EAAE4G,YAAc,SAAUhG,EAAMV,GAE9B,GADAqb,IACI,gBAAoB3a,IAAQ,kBAAsBV,GACpD,KAAM,IAAIoE,OAAM,kBAClBhB,QAAO2H,QAAQvE,IAAI/F,EAAUC,GAAOV,EAAGE,yBAGzCJ,EAAE6G,cAAgB,SAAU2P,EAAU5V,GAEpC,GADA2a,MACM/E,YAlERqC,IAkE+CrC,YAAoB3D,IAC/D,KAAM,IAAIvO,OAAM,6BAClBkS,GAAS9P,IAAI/F,EAAUC,KAGzBZ,EAAE0b,eAAiB,SAAU9a,GAC3B2a,IACAjY,OAAO2H,QAAQvE,IAAI/F,EAAUC,IAC7BZ,EAAE,8BAA8B2V,KAAK,WACnC,GAAIa,GAAWxW,EAAED,MAAMoZ,KAAK,UACxB3C,IACFA,EAAS9P,IAAI/F,EAAUC,OAM7BZ,EAAE2b,KAAO,SAAU/a,EAAM4V,GF0gErB,GAAIoF,EEzgENL,IACA,IAAIM,GAAiBrF,YArFvBqC,IAqF6DrC,YAAoB3D,GAC3ExS,EAAOxB,MAAMO,UAAUC,MAAMiB,KAAKC,UAAWsb,EAAgB,EAAI,EACrExb,GAAKG,QAAQG,EAAUC,IAClBib,IACHrF,EAAWlT,OAAO2H,UAEpB2Q,EAAApF,GAASrR,QAAA1E,MAAAmb,EAAAjd,mBAAW0B,IavFtBL,GAAEuJ,QAAO,EAAM0B,GACb6Q,iBACEC,WACE7b,GAAI,SAAU8b,GAKZ,MAAOA,GAAIC,QAAU,KAAOD,EAAIC,OAAS,KAE3CtR,KAAK,GAEPuR,SACEhc,GAAI,SAAU8b,GAEZ,MAAOA,GAAIC,OAAS,KAAOD,EAAIC,QAAU,KAE3CtR,KAAK,IAITwR,kBAAmB,SAAUvb,EAAMV,EAAIyK,EAAK5E,GAO1C,MANAkF,GAAQ6Q,gBAAgBlb,IACtBV,GAAIA,EACJyK,IAAKA,IAAO,EACZ5E,QAASA,OAGJhG,QAKXkL,EAAQD,aAAa,UACnBnC,iBACE,GAAI,SACJkD,UAAa,SACbmQ,QAAW,UACXnW,QAAW,UAGb6D,eAAgB,SAAU5H,EAAO2I,EAAK5E,EAASyQ,GAC7C,GACI4F,GACAC,EAFAlD,KAGApN,EAAYhG,EAAQgG,aAAc,IAAShG,EAAQmW,QAAU,UAAY,UAE7E,IAAI,mBAAuBjR,GAAQ6Q,gBAAgB/P,GACjD,KAAM,IAAIzH,OAAM,0CAA4CyH,EAAY,IAE1EpB,GAAMM,EAAQ6Q,gBAAgB/P,GAAWpB,KAAOA,EAG5CA,EAAI2R,QAAQ,WAAa,GAC3B3R,EAAMA,EAAI5H,QAAQ,UAAWwZ,mBAAmBva,IAEhDmX,EAAK3C,EAASpV,SAASD,KAAK,SAAWqV,EAASpV,SAASD,KAAK,OAASa,CAIzE,IAAIwa,GAAgBxc,EAAEuJ,QAAO,EAAMxD,EAAQA,YAAgBkF,EAAQ6Q,gBAAgB/P,GAAWhG,QAG9FqW,GAAcpc,EAAEuJ,QAAO,MACrBoB,IAAKA,EACLwO,KAAMA,EACNpP,KAAM,OACLyS,GAGHhG,EAASrR,QAAQ,oBAAqBqR,EAAU4F,GAEhDC,EAAMrc,EAAEyc,MAAML,GAGV,mBAAuBnR,GAAQyR,eACjCzR,EAAQyR,gBAGV,IAAIV,GAAM/Q,EAAQyR,aAAaL,GAAOpR,EAAQyR,aAAaL,IAAQrc,EAAE2c,KAAKP,GAEtEQ,EAAY,WACd,GAAIpY,GAASyG,EAAQ6Q,gBAAgB/P,GAAW7L,GAAGI,KAAKkW,EAAUwF,EAAKrR,EAAK5E,EAG5E,OAFKvB,KACHA,EAASxE,EAAE2U,WAAWC,UACjB5U,EAAE8U,KAAKtQ,GAGhB,OAAOwX,GAAIa,KAAKD,EAAWA,IAG7B3S,SAAU,KAGZgB,EAAQ5E,GAAG,cAAe,WACxB4E,EAAQyR,kBAGVpZ,OAAO6W,cAAcgC,kBAAoB,WAEvC,MADAvB,cAAapX,SAAS,4HACfyH,EAAQkR,kBAAA1b,MAARwK,EAA6B1K,YCpGtC0K,EAAQO,YAAY,MAClBY,eAAgB,kCAChBrC,MACES,MAAc,sCACdG,IAAc,oCACd1C,OAAc,uCACdF,QAAc,wCACd0C,OAAc,+BACdC,SAAc,sCAEhB6B,SAAgB,kCAChBC,SAAgB,0BAChBa,QAAgB,kCAChBI,IAAgB,oDAChB1C,IAAgB,kDAChBH,MAAgB,0CAChB0C,UAAgB,iEAChBE,UAAgB,iEAChBvO,OAAgB,gFAChByO,SAAgB,uCAChBC,SAAgB,uCAChBC,MAAgB,6CAChBC,QAAgB,mCAGlB5C,EAAQC,UAAU,KC7BlB,IAAArL,GAAAoL,ChBuzEE,OAAOpL", "file": "parsley.min.js", "sourcesContent": [null, "(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory(require('jquery')) :\n  typeof define === 'function' && define.amd ? define(['jquery'], factory) :\n  global.parsley = factory(global.$)\n}(this, function ($) { 'use strict';\n\n  var globalID = 1;\n  var pastWarnings = {};\n\n  var ParsleyUtils__ParsleyUtils = {\n    // Parsley DOM-API\n    // returns object from dom attributes and values\n    attr: function ($element, namespace, obj) {\n      var i;\n      var attribute;\n      var attributes;\n      var regex = new RegExp('^' + namespace, 'i');\n\n      if ('undefined' === typeof obj)\n        obj = {};\n      else {\n        // Clear all own properties. This won't affect prototype's values\n        for (i in obj) {\n          if (obj.hasOwnProperty(i))\n            delete obj[i];\n        }\n      }\n\n      if ('undefined' === typeof $element || 'undefined' === typeof $element[0])\n        return obj;\n\n      attributes = $element[0].attributes;\n      for (i = attributes.length; i--; ) {\n        attribute = attributes[i];\n\n        if (attribute && attribute.specified && regex.test(attribute.name)) {\n          obj[this.camelize(attribute.name.slice(namespace.length))] = this.deserializeValue(attribute.value);\n        }\n      }\n\n      return obj;\n    },\n\n    checkAttr: function ($element, namespace, checkAttr) {\n      return $element.is('[' + namespace + checkAttr + ']');\n    },\n\n    setAttr: function ($element, namespace, attr, value) {\n      $element[0].setAttribute(this.dasherize(namespace + attr), String(value));\n    },\n\n    generateID: function () {\n      return '' + globalID++;\n    },\n\n    /** Third party functions **/\n    // Zepto deserialize function\n    deserializeValue: function (value) {\n      var num;\n\n      try {\n        return value ?\n          value == \"true\" ||\n          (value == \"false\" ? false :\n          value == \"null\" ? null :\n          !isNaN(num = Number(value)) ? num :\n          /^[\\[\\{]/.test(value) ? $.parseJSON(value) :\n          value)\n          : value;\n      } catch (e) { return value; }\n    },\n\n    // Zepto camelize function\n    camelize: function (str) {\n      return str.replace(/-+(.)?/g, function (match, chr) {\n        return chr ? chr.toUpperCase() : '';\n      });\n    },\n\n    // Zepto dasherize function\n    dasherize: function (str) {\n      return str.replace(/::/g, '/')\n        .replace(/([A-Z]+)([A-Z][a-z])/g, '$1_$2')\n        .replace(/([a-z\\d])([A-Z])/g, '$1_$2')\n        .replace(/_/g, '-')\n        .toLowerCase();\n    },\n\n    warn: function () {\n      if (window.console && 'function' === typeof window.console.warn)\n        window.console.warn(...arguments);\n    },\n\n    warnOnce: function(msg) {\n      if (!pastWarnings[msg]) {\n        pastWarnings[msg] = true;\n        this.warn(...arguments);\n      }\n    },\n\n    _resetWarnings: function () {\n      pastWarnings = {};\n    },\n\n    trimString: function(string) {\n      return string.replace(/^\\s+|\\s+$/g, '');\n    },\n\n    namespaceEvents: function(events, namespace) {\n      events = this.trimString(events || '').split(/\\s+/);\n      if (!events[0])\n        return '';\n      return $.map(events, evt => { return `${evt}.${namespace}`; }).join(' ');\n    },\n\n    // Object.create polyfill, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/create#Polyfill\n    objectCreate: Object.create || (function () {\n      var Object = function () {};\n      return function (prototype) {\n        if (arguments.length > 1) {\n          throw Error('Second argument not supported');\n        }\n        if (typeof prototype != 'object') {\n          throw TypeError('Argument must be an object');\n        }\n        Object.prototype = prototype;\n        var result = new Object();\n        Object.prototype = null;\n        return result;\n      };\n    })()\n  };\n\n  var ParsleyUtils__default = ParsleyUtils__ParsleyUtils;\n\n  // All these options could be overriden and specified directly in DOM using\n  // `data-parsley-` default DOM-API\n  // eg: `inputs` can be set in DOM using `data-parsley-inputs=\"input, textarea\"`\n  // eg: `data-parsley-stop-on-first-failing-constraint=\"false\"`\n\n  var ParsleyDefaults = {\n    // ### General\n\n    // Default data-namespace for DOM API\n    namespace: 'data-parsley-',\n\n    // Supported inputs by default\n    inputs: 'input, textarea, select',\n\n    // Excluded inputs by default\n    excluded: 'input[type=button], input[type=submit], input[type=reset], input[type=hidden]',\n\n    // Stop validating field on highest priority failing constraint\n    priorityEnabled: true,\n\n    // ### Field only\n\n    // identifier used to group together inputs (e.g. radio buttons...)\n    multiple: null,\n\n    // identifier (or array of identifiers) used to validate only a select group of inputs\n    group: null,\n\n    // ### UI\n    // Enable\\Disable error messages\n    uiEnabled: true,\n\n    // Key events threshold before validation\n    validationThreshold: 3,\n\n    // Focused field on form validation error. 'first'|'last'|'none'\n    focus: 'first',\n\n    // event(s) that will trigger validation before first failure. eg: `input`...\n    trigger: false,\n\n    // event(s) that will trigger validation after first failure.\n    triggerAfterFailure: 'input',\n\n    // Class that would be added on every failing validation Parsley field\n    errorClass: 'parsley-error',\n\n    // Same for success validation\n    successClass: 'parsley-success',\n\n    // Return the `$element` that will receive these above success or error classes\n    // Could also be (and given directly from DOM) a valid selector like `'#div'`\n    classHandler: function (ParsleyField) {},\n\n    // Return the `$element` where errors will be appended\n    // Could also be (and given directly from DOM) a valid selector like `'#div'`\n    errorsContainer: function (ParsleyField) {},\n\n    // ul elem that would receive errors' list\n    errorsWrapper: '<ul class=\"parsley-errors-list\"></ul>',\n\n    // li elem that would receive error message\n    errorTemplate: '<li></li>'\n  };\n\n  var ParsleyAbstract = function () {};\n\n  ParsleyAbstract.prototype = {\n    asyncSupport: true, // Deprecated\n\n    actualizeOptions: function () {\n      ParsleyUtils__default.attr(this.$element, this.options.namespace, this.domOptions);\n      if (this.parent && this.parent.actualizeOptions)\n        this.parent.actualizeOptions();\n      return this;\n    },\n\n    _resetOptions: function (initOptions) {\n      this.domOptions = ParsleyUtils__default.objectCreate(this.parent.options);\n      this.options = ParsleyUtils__default.objectCreate(this.domOptions);\n      // Shallow copy of ownProperties of initOptions:\n      for (var i in initOptions) {\n        if (initOptions.hasOwnProperty(i))\n          this.options[i] = initOptions[i];\n      }\n      this.actualizeOptions();\n    },\n\n    _listeners: null,\n\n    // Register a callback for the given event name\n    // Callback is called with context as the first argument and the `this`\n    // The context is the current parsley instance, or window.Parsley if global\n    // A return value of `false` will interrupt the calls\n    on: function (name, fn) {\n      this._listeners = this._listeners || {};\n      var queue = this._listeners[name] = this._listeners[name] || [];\n      queue.push(fn);\n\n      return this;\n    },\n\n    // Deprecated. Use `on` instead\n    subscribe: function(name, fn) {\n      $.listenTo(this, name.toLowerCase(), fn);\n    },\n\n    // Unregister a callback (or all if none is given) for the given event name\n    off: function (name, fn) {\n      var queue = this._listeners && this._listeners[name];\n      if (queue) {\n        if (!fn) {\n          delete this._listeners[name];\n        } else {\n          for (var i = queue.length; i--; )\n            if (queue[i] === fn)\n              queue.splice(i, 1);\n        }\n      }\n      return this;\n    },\n\n    // Deprecated. Use `off`\n    unsubscribe: function(name, fn) {\n      $.unsubscribeTo(this, name.toLowerCase());\n    },\n\n    // Trigger an event of the given name\n    // A return value of `false` interrupts the callback chain\n    // Returns false if execution was interrupted\n    trigger: function (name, target, extraArg) {\n      target = target || this;\n      var queue = this._listeners && this._listeners[name];\n      var result;\n      var parentResult;\n      if (queue) {\n        for (var i = queue.length; i--; ) {\n          result = queue[i].call(target, target, extraArg);\n          if (result === false) return result;\n        }\n      }\n      if (this.parent) {\n        return this.parent.trigger(name, target, extraArg);\n      }\n      return true;\n    },\n\n    // Reset UI\n    reset: function () {\n      // Field case: just emit a reset event for UI\n      if ('ParsleyForm' !== this.__class__) {\n        this._resetUI();\n        return this._trigger('reset');\n      }\n\n      // Form case: emit a reset event for each field\n      for (var i = 0; i < this.fields.length; i++)\n        this.fields[i].reset();\n\n      this._trigger('reset');\n    },\n\n    // Destroy Parsley instance (+ UI)\n    destroy: function () {\n      // Field case: emit destroy event to clean UI and then destroy stored instance\n      this._destroyUI();\n      if ('ParsleyForm' !== this.__class__) {\n        this.$element.removeData('Parsley');\n        this.$element.removeData('ParsleyFieldMultiple');\n        this._trigger('destroy');\n\n        return;\n      }\n\n      // Form case: destroy all its fields and then destroy stored instance\n      for (var i = 0; i < this.fields.length; i++)\n        this.fields[i].destroy();\n\n      this.$element.removeData('Parsley');\n      this._trigger('destroy');\n    },\n\n    asyncIsValid: function (group, force) {\n      ParsleyUtils__default.warnOnce(\"asyncIsValid is deprecated; please use whenValid instead\");\n      return this.whenValid({group, force});\n    },\n\n    _findRelated: function () {\n      return this.options.multiple ?\n        this.parent.$element.find(`[${this.options.namespace}multiple=\"${this.options.multiple}\"]`)\n      : this.$element;\n    }\n  };\n\n  var requirementConverters = {\n    string: function(string) {\n      return string;\n    },\n    integer: function(string) {\n      if (isNaN(string))\n        throw 'Requirement is not an integer: \"' + string + '\"';\n      return parseInt(string, 10);\n    },\n    number: function(string) {\n      if (isNaN(string))\n        throw 'Requirement is not a number: \"' + string + '\"';\n      return parseFloat(string);\n    },\n    reference: function(string) { // Unused for now\n      var result = $(string);\n      if (result.length === 0)\n        throw 'No such reference: \"' + string + '\"';\n      return result;\n    },\n    boolean: function(string) {\n      return string !== 'false';\n    },\n    object: function(string) {\n      return ParsleyUtils__default.deserializeValue(string);\n    },\n    regexp: function(regexp) {\n      var flags = '';\n\n      // Test if RegExp is literal, if not, nothing to be done, otherwise, we need to isolate flags and pattern\n      if (/^\\/.*\\/(?:[gimy]*)$/.test(regexp)) {\n        // Replace the regexp literal string with the first match group: ([gimy]*)\n        // If no flag is present, this will be a blank string\n        flags = regexp.replace(/.*\\/([gimy]*)$/, '$1');\n        // Again, replace the regexp literal string with the first match group:\n        // everything excluding the opening and closing slashes and the flags\n        regexp = regexp.replace(new RegExp('^/(.*?)/' + flags + '$'), '$1');\n      } else {\n        // Anchor regexp:\n        regexp = '^' + regexp + '$';\n      }\n      return new RegExp(regexp, flags);\n    }\n  };\n\n  var convertArrayRequirement = function(string, length) {\n    var m = string.match(/^\\s*\\[(.*)\\]\\s*$/);\n    if (!m)\n      throw 'Requirement is not an array: \"' + string + '\"';\n    var values = m[1].split(',').map(ParsleyUtils__default.trimString);\n    if (values.length !== length)\n      throw 'Requirement has ' + values.length + ' values when ' + length + ' are needed';\n    return values;\n  };\n\n  var convertRequirement = function(requirementType, string) {\n    var converter = requirementConverters[requirementType || 'string'];\n    if (!converter)\n      throw 'Unknown requirement specification: \"' + requirementType + '\"';\n    return converter(string);\n  };\n\n  var convertExtraOptionRequirement = function(requirementSpec, string, extraOptionReader) {\n    var main = null;\n    var extra = {};\n    for (var key in requirementSpec) {\n      if (key) {\n        var value = extraOptionReader(key);\n        if ('string' === typeof value)\n          value = convertRequirement(requirementSpec[key], value);\n        extra[key] = value;\n      } else {\n        main = convertRequirement(requirementSpec[key], string);\n      }\n    }\n    return [main, extra];\n  };\n\n  // A Validator needs to implement the methods `validate` and `parseRequirements`\n\n  var ParsleyValidator = function(spec) {\n    $.extend(true, this, spec);\n  };\n\n  ParsleyValidator.prototype = {\n    // Returns `true` iff the given `value` is valid according the given requirements.\n    validate: function(value, requirementFirstArg) {\n      if (this.fn) { // Legacy style validator\n\n        if (arguments.length > 3)  // If more args then value, requirement, instance...\n          requirementFirstArg = [].slice.call(arguments, 1, -1);  // Skip first arg (value) and last (instance), combining the rest\n        return this.fn.call(this, value, requirementFirstArg);\n      }\n\n      if ($.isArray(value)) {\n        if (!this.validateMultiple)\n          throw 'Validator `' + this.name + '` does not handle multiple values';\n        return this.validateMultiple(...arguments);\n      } else {\n        if (this.validateNumber) {\n          if (isNaN(value))\n            return false;\n          arguments[0] = parseFloat(arguments[0]);\n          return this.validateNumber(...arguments);\n        }\n        if (this.validateString) {\n          return this.validateString(...arguments);\n        }\n        throw 'Validator `' + this.name + '` only handles multiple values';\n      }\n    },\n\n    // Parses `requirements` into an array of arguments,\n    // according to `this.requirementType`\n    parseRequirements: function(requirements, extraOptionReader) {\n      if ('string' !== typeof requirements) {\n        // Assume requirement already parsed\n        // but make sure we return an array\n        return $.isArray(requirements) ? requirements : [requirements];\n      }\n      var type = this.requirementType;\n      if ($.isArray(type)) {\n        var values = convertArrayRequirement(requirements, type.length);\n        for (var i = 0; i < values.length; i++)\n          values[i] = convertRequirement(type[i], values[i]);\n        return values;\n      } else if ($.isPlainObject(type)) {\n        return convertExtraOptionRequirement(type, requirements, extraOptionReader);\n      } else {\n        return [convertRequirement(type, requirements)];\n      }\n    },\n    // Defaults:\n    requirementType: 'string',\n\n    priority: 2\n\n  };\n\n  var ParsleyValidatorRegistry = function (validators, catalog) {\n    this.__class__ = 'ParsleyValidatorRegistry';\n\n    // Default Parsley locale is en\n    this.locale = 'en';\n\n    this.init(validators || {}, catalog || {});\n  };\n\n  var typeRegexes =  {\n    email: /^((([a-z]|\\d|[!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])+(\\.([a-z]|\\d|[!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])+)*)|((\\x22)((((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(([\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x7f]|\\x21|[\\x23-\\x5b]|[\\x5d-\\x7e]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(\\\\([\\x01-\\x09\\x0b\\x0c\\x0d-\\x7f]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]))))*(((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(\\x22)))@((([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.)+(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))$/i,\n\n    // Follow https://www.w3.org/TR/html5/infrastructure.html#floating-point-numbers\n    number: /^-?(\\d*\\.)?\\d+(e[-+]?\\d+)?$/i,\n\n    integer: /^-?\\d+$/,\n\n    digits: /^\\d+$/,\n\n    alphanum: /^\\w+$/i,\n\n    url: new RegExp(\n        \"^\" +\n          // protocol identifier\n          \"(?:(?:https?|ftp)://)?\" + // ** mod: make scheme optional\n          // user:pass authentication\n          \"(?:\\\\S+(?::\\\\S*)?@)?\" +\n          \"(?:\" +\n            // IP address exclusion\n            // private & local networks\n            // \"(?!(?:10|127)(?:\\\\.\\\\d{1,3}){3})\" +   // ** mod: allow local networks\n            // \"(?!(?:169\\\\.254|192\\\\.168)(?:\\\\.\\\\d{1,3}){2})\" +  // ** mod: allow local networks\n            // \"(?!172\\\\.(?:1[6-9]|2\\\\d|3[0-1])(?:\\\\.\\\\d{1,3}){2})\" +  // ** mod: allow local networks\n            // IP address dotted notation octets\n            // excludes loopback network 0.0.0.0\n            // excludes reserved space >= *********\n            // excludes network & broacast addresses\n            // (first & last IP address of each class)\n            \"(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])\" +\n            \"(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}\" +\n            \"(?:\\\\.(?:[1-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))\" +\n          \"|\" +\n            // host name\n            \"(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)\" +\n            // domain name\n            \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*\" +\n            // TLD identifier\n            \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))\" +\n          \")\" +\n          // port number\n          \"(?::\\\\d{2,5})?\" +\n          // resource path\n          \"(?:/\\\\S*)?\" +\n        \"$\", 'i'\n      )\n  };\n  typeRegexes.range = typeRegexes.number;\n\n  // See http://stackoverflow.com/a/10454560/8279\n  var decimalPlaces = num => {\n    var match = ('' + num).match(/(?:\\.(\\d+))?(?:[eE]([+-]?\\d+))?$/);\n    if (!match) { return 0; }\n    return Math.max(\n         0,\n         // Number of digits right of decimal point.\n         (match[1] ? match[1].length : 0) -\n         // Adjust for scientific notation.\n         (match[2] ? +match[2] : 0));\n  };\n\n  ParsleyValidatorRegistry.prototype = {\n    init: function (validators, catalog) {\n      this.catalog = catalog;\n      // Copy prototype's validators:\n      this.validators = $.extend({}, this.validators);\n\n      for (var name in validators)\n        this.addValidator(name, validators[name].fn, validators[name].priority);\n\n      window.Parsley.trigger('parsley:validator:init');\n    },\n\n    // Set new messages locale if we have dictionary loaded in ParsleyConfig.i18n\n    setLocale: function (locale) {\n      if ('undefined' === typeof this.catalog[locale])\n        throw new Error(locale + ' is not available in the catalog');\n\n      this.locale = locale;\n\n      return this;\n    },\n\n    // Add a new messages catalog for a given locale. Set locale for this catalog if set === `true`\n    addCatalog: function (locale, messages, set) {\n      if ('object' === typeof messages)\n        this.catalog[locale] = messages;\n\n      if (true === set)\n        return this.setLocale(locale);\n\n      return this;\n    },\n\n    // Add a specific message for a given constraint in a given locale\n    addMessage: function (locale, name, message) {\n      if ('undefined' === typeof this.catalog[locale])\n        this.catalog[locale] = {};\n\n      this.catalog[locale][name] = message;\n\n      return this;\n    },\n\n    // Add messages for a given locale\n    addMessages: function (locale, nameMessageObject) {\n      for (var name in nameMessageObject)\n        this.addMessage(locale, name, nameMessageObject[name]);\n\n      return this;\n    },\n\n    // Add a new validator\n    //\n    //    addValidator('custom', {\n    //        requirementType: ['integer', 'integer'],\n    //        validateString: function(value, from, to) {},\n    //        priority: 22,\n    //        messages: {\n    //          en: \"Hey, that's no good\",\n    //          fr: \"Aye aye, pas bon du tout\",\n    //        }\n    //    })\n    //\n    // Old API was addValidator(name, function, priority)\n    //\n    addValidator: function (name, arg1, arg2) {\n      if (this.validators[name])\n        ParsleyUtils__default.warn('Validator \"' + name + '\" is already defined.');\n      else if (ParsleyDefaults.hasOwnProperty(name)) {\n        ParsleyUtils__default.warn('\"' + name + '\" is a restricted keyword and is not a valid validator name.');\n        return;\n      }\n      return this._setValidator(...arguments);\n    },\n\n    updateValidator: function (name, arg1, arg2) {\n      if (!this.validators[name]) {\n        ParsleyUtils__default.warn('Validator \"' + name + '\" is not already defined.');\n        return this.addValidator(...arguments);\n      }\n      return this._setValidator(this, arguments);\n    },\n\n    removeValidator: function (name) {\n      if (!this.validators[name])\n        ParsleyUtils__default.warn('Validator \"' + name + '\" is not defined.');\n\n      delete this.validators[name];\n\n      return this;\n    },\n\n    _setValidator: function (name, validator, priority) {\n      if ('object' !== typeof validator) {\n        // Old style validator, with `fn` and `priority`\n        validator = {\n          fn: validator,\n          priority: priority\n        };\n      }\n      if (!validator.validate) {\n        validator = new ParsleyValidator(validator);\n      }\n      this.validators[name] = validator;\n\n      for (var locale in validator.messages || {})\n        this.addMessage(locale, name, validator.messages[locale]);\n\n      return this;\n    },\n\n    getErrorMessage: function (constraint) {\n      var message;\n\n      // Type constraints are a bit different, we have to match their requirements too to find right error message\n      if ('type' === constraint.name) {\n        var typeMessages = this.catalog[this.locale][constraint.name] || {};\n        message = typeMessages[constraint.requirements];\n      } else\n        message = this.formatMessage(this.catalog[this.locale][constraint.name], constraint.requirements);\n\n      return message || this.catalog[this.locale].defaultMessage || this.catalog.en.defaultMessage;\n    },\n\n    // Kind of light `sprintf()` implementation\n    formatMessage: function (string, parameters) {\n      if ('object' === typeof parameters) {\n        for (var i in parameters)\n          string = this.formatMessage(string, parameters[i]);\n\n        return string;\n      }\n\n      return 'string' === typeof string ? string.replace(/%s/i, parameters) : '';\n    },\n\n    // Here is the Parsley default validators list.\n    // A validator is an object with the following key values:\n    //  - priority: an integer\n    //  - requirement: 'string' (default), 'integer', 'number', 'regexp' or an Array of these\n    //  - validateString, validateMultiple, validateNumber: functions returning `true`, `false` or a promise\n    // Alternatively, a validator can be a function that returns such an object\n    //\n    validators: {\n      notblank: {\n        validateString: function(value) {\n          return /\\S/.test(value);\n        },\n        priority: 2\n      },\n      required: {\n        validateMultiple: function(values) {\n          return values.length > 0;\n        },\n        validateString: function(value) {\n          return /\\S/.test(value);\n        },\n        priority: 512\n      },\n      type: {\n        validateString: function(value, type, {step = '1', base = 0} = {}) {\n          var regex = typeRegexes[type];\n          if (!regex) {\n            throw new Error('validator type `' + type + '` is not supported');\n          }\n          if (!regex.test(value))\n            return false;\n          if ('number' === type) {\n            if (!/^any$/i.test(step || '')) {\n              var nb = Number(value);\n              var decimals = Math.max(decimalPlaces(step), decimalPlaces(base));\n              if (decimalPlaces(nb) > decimals) // Value can't have too many decimals\n                return false;\n              // Be careful of rounding errors by using integers.\n              var toInt = f => { return Math.round(f * Math.pow(10, decimals)); };\n              if ((toInt(nb) - toInt(base)) % toInt(step) != 0)\n                return false;\n            }\n          }\n          return true;\n        },\n        requirementType: {\n          '': 'string',\n          step: 'string',\n          base: 'number'\n        },\n        priority: 256\n      },\n      pattern: {\n        validateString: function(value, regexp) {\n          return regexp.test(value);\n        },\n        requirementType: 'regexp',\n        priority: 64\n      },\n      minlength: {\n        validateString: function (value, requirement) {\n          return value.length >= requirement;\n        },\n        requirementType: 'integer',\n        priority: 30\n      },\n      maxlength: {\n        validateString: function (value, requirement) {\n          return value.length <= requirement;\n        },\n        requirementType: 'integer',\n        priority: 30\n      },\n      length: {\n        validateString: function (value, min, max) {\n          return value.length >= min && value.length <= max;\n        },\n        requirementType: ['integer', 'integer'],\n        priority: 30\n      },\n      mincheck: {\n        validateMultiple: function (values, requirement) {\n          return values.length >= requirement;\n        },\n        requirementType: 'integer',\n        priority: 30\n      },\n      maxcheck: {\n        validateMultiple: function (values, requirement) {\n          return values.length <= requirement;\n        },\n        requirementType: 'integer',\n        priority: 30\n      },\n      check: {\n        validateMultiple: function (values, min, max) {\n          return values.length >= min && values.length <= max;\n        },\n        requirementType: ['integer', 'integer'],\n        priority: 30\n      },\n      min: {\n        validateNumber: function (value, requirement) {\n          return value >= requirement;\n        },\n        requirementType: 'number',\n        priority: 30\n      },\n      max: {\n        validateNumber: function (value, requirement) {\n          return value <= requirement;\n        },\n        requirementType: 'number',\n        priority: 30\n      },\n      range: {\n        validateNumber: function (value, min, max) {\n          return value >= min && value <= max;\n        },\n        requirementType: ['number', 'number'],\n        priority: 30\n      },\n      equalto: {\n        validateString: function (value, refOrValue) {\n          var $reference = $(refOrValue);\n          if ($reference.length)\n            return value === $reference.val();\n          else\n            return value === refOrValue;\n        },\n        priority: 256\n      }\n    }\n  };\n\n  var ParsleyUI = {};\n\n  var diffResults = function (newResult, oldResult, deep) {\n    var added = [];\n    var kept = [];\n\n    for (var i = 0; i < newResult.length; i++) {\n      var found = false;\n\n      for (var j = 0; j < oldResult.length; j++)\n        if (newResult[i].assert.name === oldResult[j].assert.name) {\n          found = true;\n          break;\n        }\n\n      if (found)\n        kept.push(newResult[i]);\n      else\n        added.push(newResult[i]);\n    }\n\n    return {\n      kept: kept,\n      added: added,\n      removed: !deep ? diffResults(oldResult, newResult, true).added : []\n    };\n  };\n\n  ParsleyUI.Form = {\n\n    _actualizeTriggers: function () {\n      this.$element.on('submit.Parsley', evt => { this.onSubmitValidate(evt); });\n      this.$element.on('click.Parsley', 'input[type=\"submit\"], button[type=\"submit\"]', evt => { this.onSubmitButton(evt); });\n\n      // UI could be disabled\n      if (false === this.options.uiEnabled)\n        return;\n\n      this.$element.attr('novalidate', '');\n    },\n\n    focus: function () {\n      this._focusedField = null;\n\n      if (true === this.validationResult || 'none' === this.options.focus)\n        return null;\n\n      for (var i = 0; i < this.fields.length; i++) {\n        var field = this.fields[i];\n        if (true !== field.validationResult && field.validationResult.length > 0 && 'undefined' === typeof field.options.noFocus) {\n          this._focusedField = field.$element;\n          if ('first' === this.options.focus)\n            break;\n        }\n      }\n\n      if (null === this._focusedField)\n        return null;\n\n      return this._focusedField.focus();\n    },\n\n    _destroyUI: function () {\n      // Reset all event listeners\n      this.$element.off('.Parsley');\n    }\n\n  };\n\n  ParsleyUI.Field = {\n\n    _reflowUI: function () {\n      this._buildUI();\n\n      // If this field doesn't have an active UI don't bother doing something\n      if (!this._ui)\n        return;\n\n      // Diff between two validation results\n      var diff = diffResults(this.validationResult, this._ui.lastValidationResult);\n\n      // Then store current validation result for next reflow\n      this._ui.lastValidationResult = this.validationResult;\n\n      // Handle valid / invalid / none field class\n      this._manageStatusClass();\n\n      // Add, remove, updated errors messages\n      this._manageErrorsMessages(diff);\n\n      // Triggers impl\n      this._actualizeTriggers();\n\n      // If field is not valid for the first time, bind keyup trigger to ease UX and quickly inform user\n      if ((diff.kept.length || diff.added.length) && !this._failedOnce) {\n        this._failedOnce = true;\n        this._actualizeTriggers();\n      }\n    },\n\n    // Returns an array of field's error message(s)\n    getErrorsMessages: function () {\n      // No error message, field is valid\n      if (true === this.validationResult)\n        return [];\n\n      var messages = [];\n\n      for (var i = 0; i < this.validationResult.length; i++)\n        messages.push(this.validationResult[i].errorMessage ||\n         this._getErrorMessage(this.validationResult[i].assert));\n\n      return messages;\n    },\n\n    // It's a goal of Parsley that this method is no longer required [#1073]\n    addError: function (name, {message, assert, updateClass = true} = {}) {\n      this._buildUI();\n      this._addError(name, {message, assert});\n\n      if (updateClass)\n        this._errorClass();\n    },\n\n    // It's a goal of Parsley that this method is no longer required [#1073]\n    updateError: function (name, {message, assert, updateClass = true} = {}) {\n      this._buildUI();\n      this._updateError(name, {message, assert});\n\n      if (updateClass)\n        this._errorClass();\n    },\n\n    // It's a goal of Parsley that this method is no longer required [#1073]\n    removeError: function (name, {updateClass = true} = {}) {\n      this._buildUI();\n      this._removeError(name);\n\n      // edge case possible here: remove a standard Parsley error that is still failing in this.validationResult\n      // but highly improbable cuz' manually removing a well Parsley handled error makes no sense.\n      if (updateClass)\n        this._manageStatusClass();\n    },\n\n    _manageStatusClass: function () {\n      if (this.hasConstraints() && this.needsValidation() && true === this.validationResult)\n        this._successClass();\n      else if (this.validationResult.length > 0)\n        this._errorClass();\n      else\n        this._resetClass();\n    },\n\n    _manageErrorsMessages: function (diff) {\n      if ('undefined' !== typeof this.options.errorsMessagesDisabled)\n        return;\n\n      // Case where we have errorMessage option that configure an unique field error message, regardless failing validators\n      if ('undefined' !== typeof this.options.errorMessage) {\n        if ((diff.added.length || diff.kept.length)) {\n          this._insertErrorWrapper();\n\n          if (0 === this._ui.$errorsWrapper.find('.parsley-custom-error-message').length)\n            this._ui.$errorsWrapper\n              .append(\n                $(this.options.errorTemplate)\n                .addClass('parsley-custom-error-message')\n              );\n\n          return this._ui.$errorsWrapper\n            .addClass('filled')\n            .find('.parsley-custom-error-message')\n            .html(this.options.errorMessage);\n        }\n\n        return this._ui.$errorsWrapper\n          .removeClass('filled')\n          .find('.parsley-custom-error-message')\n          .remove();\n      }\n\n      // Show, hide, update failing constraints messages\n      for (var i = 0; i < diff.removed.length; i++)\n        this._removeError(diff.removed[i].assert.name);\n\n      for (i = 0; i < diff.added.length; i++)\n        this._addError(diff.added[i].assert.name, {message: diff.added[i].errorMessage, assert: diff.added[i].assert});\n\n      for (i = 0; i < diff.kept.length; i++)\n        this._updateError(diff.kept[i].assert.name, {message: diff.kept[i].errorMessage, assert: diff.kept[i].assert});\n    },\n\n\n    _addError: function (name, {message, assert}) {\n      this._insertErrorWrapper();\n      this._ui.$errorsWrapper\n        .addClass('filled')\n        .append(\n          $(this.options.errorTemplate)\n          .addClass('parsley-' + name)\n          .html(message || this._getErrorMessage(assert))\n        );\n    },\n\n    _updateError: function (name, {message, assert}) {\n      this._ui.$errorsWrapper\n        .addClass('filled')\n        .find('.parsley-' + name)\n        .html(message || this._getErrorMessage(assert));\n    },\n\n    _removeError: function (name) {\n      this._ui.$errorsWrapper\n        .removeClass('filled')\n        .find('.parsley-' + name)\n        .remove();\n    },\n\n    _getErrorMessage: function (constraint) {\n      var customConstraintErrorMessage = constraint.name + 'Message';\n\n      if ('undefined' !== typeof this.options[customConstraintErrorMessage])\n        return window.Parsley.formatMessage(this.options[customConstraintErrorMessage], constraint.requirements);\n\n      return window.Parsley.getErrorMessage(constraint);\n    },\n\n    _buildUI: function () {\n      // UI could be already built or disabled\n      if (this._ui || false === this.options.uiEnabled)\n        return;\n\n      var _ui = {};\n\n      // Give field its Parsley id in DOM\n      this.$element.attr(this.options.namespace + 'id', this.__id__);\n\n      /** Generate important UI elements and store them in this **/\n      // $errorClassHandler is the $element that woul have parsley-error and parsley-success classes\n      _ui.$errorClassHandler = this._manageClassHandler();\n\n      // $errorsWrapper is a div that would contain the various field errors, it will be appended into $errorsContainer\n      _ui.errorsWrapperId = 'parsley-id-' + (this.options.multiple ? 'multiple-' + this.options.multiple : this.__id__);\n      _ui.$errorsWrapper = $(this.options.errorsWrapper).attr('id', _ui.errorsWrapperId);\n\n      // ValidationResult UI storage to detect what have changed bwt two validations, and update DOM accordingly\n      _ui.lastValidationResult = [];\n      _ui.validationInformationVisible = false;\n\n      // Store it in this for later\n      this._ui = _ui;\n    },\n\n    // Determine which element will have `parsley-error` and `parsley-success` classes\n    _manageClassHandler: function () {\n      // An element selector could be passed through DOM with `data-parsley-class-handler=#foo`\n      if ('string' === typeof this.options.classHandler && $(this.options.classHandler).length)\n        return $(this.options.classHandler);\n\n      // Class handled could also be determined by function given in Parsley options\n      var $handler = this.options.classHandler.call(this, this);\n\n      // If this function returned a valid existing DOM element, go for it\n      if ('undefined' !== typeof $handler && $handler.length)\n        return $handler;\n\n      // Otherwise, if simple element (input, texatrea, select...) it will perfectly host the classes\n      if (!this.options.multiple || this.$element.is('select'))\n        return this.$element;\n\n      // But if multiple element (radio, checkbox), that would be their parent\n      return this.$element.parent();\n    },\n\n    _insertErrorWrapper: function () {\n      var $errorsContainer;\n\n      // Nothing to do if already inserted\n      if (0 !== this._ui.$errorsWrapper.parent().length)\n        return this._ui.$errorsWrapper.parent();\n\n      if ('string' === typeof this.options.errorsContainer) {\n        if ($(this.options.errorsContainer).length)\n          return $(this.options.errorsContainer).append(this._ui.$errorsWrapper);\n        else\n          ParsleyUtils__default.warn('The errors container `' + this.options.errorsContainer + '` does not exist in DOM');\n      } else if ('function' === typeof this.options.errorsContainer)\n        $errorsContainer = this.options.errorsContainer.call(this, this);\n\n      if ('undefined' !== typeof $errorsContainer && $errorsContainer.length)\n        return $errorsContainer.append(this._ui.$errorsWrapper);\n\n      var $from = this.$element;\n      if (this.options.multiple)\n        $from = $from.parent();\n      return $from.after(this._ui.$errorsWrapper);\n    },\n\n    _actualizeTriggers: function () {\n      var $toBind = this._findRelated();\n\n      // Remove Parsley events already bound on this field\n      $toBind.off('.Parsley');\n      if (this._failedOnce)\n        $toBind.on(ParsleyUtils__default.namespaceEvents(this.options.triggerAfterFailure, 'Parsley'), () => {\n          this.validate();\n        });\n      else {\n        $toBind.on(ParsleyUtils__default.namespaceEvents(this.options.trigger, 'Parsley'), event => {\n          this._eventValidate(event);\n        });\n      }\n    },\n\n    _eventValidate: function (event) {\n      // For keyup, keypress, keydown, input... events that could be a little bit obstrusive\n      // do not validate if val length < min threshold on first validation. Once field have been validated once and info\n      // about success or failure have been displayed, always validate with this trigger to reflect every yalidation change.\n      if (/key|input/.test(event.type))\n        if (!(this._ui && this._ui.validationInformationVisible) && this.getValue().length <= this.options.validationThreshold)\n          return;\n\n      this.validate();\n    },\n\n    _resetUI: function () {\n      // Reset all event listeners\n      this._failedOnce = false;\n      this._actualizeTriggers();\n\n      // Nothing to do if UI never initialized for this field\n      if ('undefined' === typeof this._ui)\n        return;\n\n      // Reset all errors' li\n      this._ui.$errorsWrapper\n        .removeClass('filled')\n        .children()\n        .remove();\n\n      // Reset validation class\n      this._resetClass();\n\n      // Reset validation flags and last validation result\n      this._ui.lastValidationResult = [];\n      this._ui.validationInformationVisible = false;\n    },\n\n    _destroyUI: function () {\n      this._resetUI();\n\n      if ('undefined' !== typeof this._ui)\n        this._ui.$errorsWrapper.remove();\n\n      delete this._ui;\n    },\n\n    _successClass: function () {\n      this._ui.validationInformationVisible = true;\n      this._ui.$errorClassHandler.removeClass(this.options.errorClass).addClass(this.options.successClass);\n    },\n    _errorClass: function () {\n      this._ui.validationInformationVisible = true;\n      this._ui.$errorClassHandler.removeClass(this.options.successClass).addClass(this.options.errorClass);\n    },\n    _resetClass: function () {\n      this._ui.$errorClassHandler.removeClass(this.options.successClass).removeClass(this.options.errorClass);\n    }\n  };\n\n  var ParsleyForm = function (element, domOptions, options) {\n    this.__class__ = 'ParsleyForm';\n    this.__id__ = ParsleyUtils__default.generateID();\n\n    this.$element = $(element);\n    this.domOptions = domOptions;\n    this.options = options;\n    this.parent = window.Parsley;\n\n    this.fields = [];\n    this.validationResult = null;\n  };\n\n  var ParsleyForm__statusMapping = {pending: null, resolved: true, rejected: false};\n\n  ParsleyForm.prototype = {\n    onSubmitValidate: function (event) {\n      // This is a Parsley generated submit event, do not validate, do not prevent, simply exit and keep normal behavior\n      if (true === event.parsley)\n        return;\n\n      // If we didn't come here through a submit button, use the first one in the form\n      var $submitSource = this._$submitSource || this.$element.find('input[type=\"submit\"], button[type=\"submit\"]').first();\n      this._$submitSource = null;\n      this.$element.find('.parsley-synthetic-submit-button').prop('disabled', true);\n      if ($submitSource.is('[formnovalidate]'))\n        return;\n\n      var promise = this.whenValidate({event});\n\n      if ('resolved' === promise.state() && false !== this._trigger('submit')) {\n        // All good, let event go through. We make this distinction because browsers\n        // differ in their handling of `submit` being called from inside a submit event [#1047]\n      } else {\n        // Rejected or pending: cancel this submit\n        event.stopImmediatePropagation();\n        event.preventDefault();\n        if ('pending' === promise.state())\n          promise.done(() => { this._submit($submitSource); });\n      }\n    },\n\n    onSubmitButton: function(event) {\n      this._$submitSource = $(event.target);\n    },\n    // internal\n    // _submit submits the form, this time without going through the validations.\n    // Care must be taken to \"fake\" the actual submit button being clicked.\n    _submit: function ($submitSource) {\n      if (false === this._trigger('submit'))\n        return;\n      // Add submit button's data\n      if ($submitSource) {\n        var $synthetic = this.$element.find('.parsley-synthetic-submit-button').prop('disabled', false);\n        if (0 === $synthetic.length)\n          $synthetic = $('<input class=\"parsley-synthetic-submit-button\" type=\"hidden\">').appendTo(this.$element);\n        $synthetic.attr({\n          name: $submitSource.attr('name'),\n          value: $submitSource.attr('value')\n        });\n      }\n\n      this.$element.trigger($.extend($.Event('submit'), {parsley: true}));\n    },\n\n    // Performs validation on fields while triggering events.\n    // @returns `true` if all validations succeeds, `false`\n    // if a failure is immediately detected, or `null`\n    // if dependant on a promise.\n    // Consider using `whenValidate` instead.\n    validate: function (options) {\n      if (arguments.length >= 1 && !$.isPlainObject(options)) {\n        ParsleyUtils__default.warnOnce('Calling validate on a parsley form without passing arguments as an object is deprecated.');\n        var [group, force, event] = arguments;\n        options = {group, force, event};\n      }\n      return ParsleyForm__statusMapping[ this.whenValidate(options).state() ];\n    },\n\n    whenValidate: function ({group, force, event} = {}) {\n      this.submitEvent = event;\n      if (event) {\n        this.submitEvent = $.extend({}, event, {preventDefault: () => {\n          ParsleyUtils__default.warnOnce(\"Using `this.submitEvent.preventDefault()` is deprecated; instead, call `this.validationResult = false`\");\n          this.validationResult = false;\n        }});\n      }\n      this.validationResult = true;\n\n      // fire validate event to eventually modify things before very validation\n      this._trigger('validate');\n\n      // Refresh form DOM options and form's fields that could have changed\n      this._refreshFields();\n\n      var promises = this._withoutReactualizingFormOptions(() => {\n        return $.map(this.fields, field => {\n          return field.whenValidate({force, group});\n        });\n      });\n\n      var promiseBasedOnValidationResult = () => {\n        var r = $.Deferred();\n        if (false === this.validationResult)\n          r.reject();\n        return r.resolve().promise();\n      };\n\n      return $.when(...promises)\n        .done(  () => { this._trigger('success'); })\n        .fail(  () => {\n          this.validationResult = false;\n          this.focus();\n          this._trigger('error');\n        })\n        .always(() => { this._trigger('validated'); })\n        .pipe(  promiseBasedOnValidationResult, promiseBasedOnValidationResult);\n    },\n\n    // Iterate over refreshed fields, and stop on first failure.\n    // Returns `true` if all fields are valid, `false` if a failure is detected\n    // or `null` if the result depends on an unresolved promise.\n    // Prefer using `whenValid` instead.\n    isValid: function (options) {\n      if (arguments.length >= 1 && !$.isPlainObject(options)) {\n        ParsleyUtils__default.warnOnce('Calling isValid on a parsley form without passing arguments as an object is deprecated.');\n        var [group, force] = arguments;\n        options = {group, force};\n      }\n      return ParsleyForm__statusMapping[ this.whenValid(options).state() ];\n    },\n\n    // Iterate over refreshed fields and validate them.\n    // Returns a promise.\n    // A validation that immediately fails will interrupt the validations.\n    whenValid: function ({group, force} = {}) {\n      this._refreshFields();\n\n      var promises = this._withoutReactualizingFormOptions(() => {\n        return $.map(this.fields, field => {\n          return field.whenValid({group, force});\n        });\n      });\n      return $.when(...promises);\n    },\n\n    _refreshFields: function () {\n      return this.actualizeOptions()._bindFields();\n    },\n\n    _bindFields: function () {\n      var oldFields = this.fields;\n\n      this.fields = [];\n      this.fieldsMappedById = {};\n\n      this._withoutReactualizingFormOptions(() => {\n        this.$element\n        .find(this.options.inputs)\n        .not(this.options.excluded)\n        .each((_, element) => {\n          var fieldInstance = new window.Parsley.Factory(element, {}, this);\n\n          // Only add valid and not excluded `ParsleyField` and `ParsleyFieldMultiple` children\n          if (('ParsleyField' === fieldInstance.__class__ || 'ParsleyFieldMultiple' === fieldInstance.__class__) && (true !== fieldInstance.options.excluded))\n            if ('undefined' === typeof this.fieldsMappedById[fieldInstance.__class__ + '-' + fieldInstance.__id__]) {\n              this.fieldsMappedById[fieldInstance.__class__ + '-' + fieldInstance.__id__] = fieldInstance;\n              this.fields.push(fieldInstance);\n            }\n        });\n\n        $(oldFields).not(this.fields).each((_, field) => {\n          field._trigger('reset');\n        });\n      });\n      return this;\n    },\n\n    // Internal only.\n    // Looping on a form's fields to do validation or similar\n    // will trigger reactualizing options on all of them, which\n    // in turn will reactualize the form's options.\n    // To avoid calling actualizeOptions so many times on the form\n    // for nothing, _withoutReactualizingFormOptions temporarily disables\n    // the method actualizeOptions on this form while `fn` is called.\n    _withoutReactualizingFormOptions: function (fn) {\n      var oldActualizeOptions = this.actualizeOptions;\n      this.actualizeOptions = function () { return this; };\n      var result = fn();\n      this.actualizeOptions = oldActualizeOptions;\n      return result;\n    },\n\n    // Internal only.\n    // Shortcut to trigger an event\n    // Returns true iff event is not interrupted and default not prevented.\n    _trigger: function (eventName) {\n      return this.trigger('form:' + eventName);\n    }\n\n  };\n\n  var ConstraintFactory = function (parsleyField, name, requirements, priority, isDomConstraint) {\n    if (!/ParsleyField/.test(parsleyField.__class__))\n      throw new Error('ParsleyField or ParsleyFieldMultiple instance expected');\n\n    var validatorSpec = window.Parsley._validatorRegistry.validators[name];\n    var validator = new ParsleyValidator(validatorSpec);\n\n    $.extend(this, {\n      validator: validator,\n      name: name,\n      requirements: requirements,\n      priority: priority || parsleyField.options[name + 'Priority'] || validator.priority,\n      isDomConstraint: true === isDomConstraint\n    });\n    this._parseRequirements(parsleyField.options);\n  };\n\n  var capitalize = function(str) {\n    var cap = str[0].toUpperCase();\n    return cap + str.slice(1);\n  };\n\n  ConstraintFactory.prototype = {\n    validate: function(value, instance) {\n      var args = this.requirementList.slice(0); // Make copy\n      args.unshift(value);\n      args.push(instance);\n      return this.validator.validate.apply(this.validator, args);\n    },\n\n    _parseRequirements: function(options) {\n      this.requirementList = this.validator.parseRequirements(this.requirements, key => {\n        return options[this.name + capitalize(key)];\n      });\n    }\n  };\n\n  var ParsleyField = function (field, domOptions, options, parsleyFormInstance) {\n    this.__class__ = 'ParsleyField';\n    this.__id__ = ParsleyUtils__default.generateID();\n\n    this.$element = $(field);\n\n    // Set parent if we have one\n    if ('undefined' !== typeof parsleyFormInstance) {\n      this.parent = parsleyFormInstance;\n    }\n\n    this.options = options;\n    this.domOptions = domOptions;\n\n    // Initialize some properties\n    this.constraints = [];\n    this.constraintsByName = {};\n    this.validationResult = [];\n\n    // Bind constraints\n    this._bindConstraints();\n  };\n\n  var parsley_field__statusMapping = {pending: null, resolved: true, rejected: false};\n\n  ParsleyField.prototype = {\n    // # Public API\n    // Validate field and trigger some events for mainly `ParsleyUI`\n    // @returns `true`, an array of the validators that failed, or\n    // `null` if validation is not finished. Prefer using whenValidate\n    validate: function (options) {\n      if (arguments.length >= 1 && !$.isPlainObject(options)) {\n        ParsleyUtils__default.warnOnce('Calling validate on a parsley field without passing arguments as an object is deprecated.');\n        options = {options};\n      }\n      var promise = this.whenValidate(options);\n      if (!promise)  // If excluded with `group` option\n        return true;\n      switch (promise.state()) {\n        case 'pending': return null;\n        case 'resolved': return true;\n        case 'rejected': return this.validationResult;\n      }\n    },\n\n    // Validate field and trigger some events for mainly `ParsleyUI`\n    // @returns a promise that succeeds only when all validations do\n    // or `undefined` if field is not in the given `group`.\n    whenValidate: function ({force, group} =  {}) {\n      // do not validate a field if not the same as given validation group\n      this.refreshConstraints();\n      if (group && !this._isInGroup(group))\n        return;\n\n      this.value = this.getValue();\n\n      // Field Validate event. `this.value` could be altered for custom needs\n      this._trigger('validate');\n\n      return this.whenValid({force, value: this.value, _refreshed: true})\n        .always(() => { this._reflowUI(); })\n        .done(() =>   { this._trigger('success'); })\n        .fail(() =>   { this._trigger('error'); })\n        .always(() => { this._trigger('validated'); });\n    },\n\n    hasConstraints: function () {\n      return 0 !== this.constraints.length;\n    },\n\n    // An empty optional field does not need validation\n    needsValidation: function (value) {\n      if ('undefined' === typeof value)\n        value = this.getValue();\n\n      // If a field is empty and not required, it is valid\n      // Except if `data-parsley-validate-if-empty` explicitely added, useful for some custom validators\n      if (!value.length && !this._isRequired() && 'undefined' === typeof this.options.validateIfEmpty)\n        return false;\n\n      return true;\n    },\n\n    _isInGroup: function (group) {\n      if ($.isArray(this.options.group))\n        return -1 !== $.inArray(group, this.options.group);\n      return this.options.group === group;\n    },\n\n    // Just validate field. Do not trigger any event.\n    // Returns `true` iff all constraints pass, `false` if there are failures,\n    // or `null` if the result can not be determined yet (depends on a promise)\n    // See also `whenValid`.\n    isValid: function (options) {\n      if (arguments.length >= 1 && !$.isPlainObject(options)) {\n        ParsleyUtils__default.warnOnce('Calling isValid on a parsley field without passing arguments as an object is deprecated.');\n        var [force, value] = arguments;\n        options = {force, value};\n      }\n      var promise = this.whenValid(options);\n      if (!promise) // Excluded via `group`\n        return true;\n      return parsley_field__statusMapping[promise.state()];\n    },\n\n    // Just validate field. Do not trigger any event.\n    // @returns a promise that succeeds only when all validations do\n    // or `undefined` if the field is not in the given `group`.\n    // The argument `force` will force validation of empty fields.\n    // If a `value` is given, it will be validated instead of the value of the input.\n    whenValid: function ({force = false, value, group, _refreshed} = {}) {\n      // Recompute options and rebind constraints to have latest changes\n      if (!_refreshed)\n        this.refreshConstraints();\n      // do not validate a field if not the same as given validation group\n      if (group && !this._isInGroup(group))\n        return;\n\n      this.validationResult = true;\n\n      // A field without constraint is valid\n      if (!this.hasConstraints())\n        return $.when();\n\n      // Value could be passed as argument, needed to add more power to 'parsley:field:validate'\n      if ('undefined' === typeof value || null === value)\n        value = this.getValue();\n\n      if (!this.needsValidation(value) && true !== force)\n        return $.when();\n\n      var groupedConstraints = this._getGroupedConstraints();\n      var promises = [];\n      $.each(groupedConstraints, (_, constraints) => {\n        // Process one group of constraints at a time, we validate the constraints\n        // and combine the promises together.\n        var promise = $.when(\n          ...$.map(constraints, constraint => this._validateConstraint(value, constraint))\n        );\n        promises.push(promise);\n        if (promise.state() === 'rejected')\n          return false; // Interrupt processing if a group has already failed\n      });\n      return $.when.apply($, promises);\n    },\n\n    // @returns a promise\n    _validateConstraint: function(value, constraint) {\n      var result = constraint.validate(value, this);\n      // Map false to a failed promise\n      if (false === result)\n        result = $.Deferred().reject();\n      // Make sure we return a promise and that we record failures\n      return $.when(result).fail(errorMessage => {\n        if (true === this.validationResult)\n          this.validationResult = [];\n        this.validationResult.push({\n          assert: constraint,\n          errorMessage: 'string' === typeof errorMessage && errorMessage\n        });\n      });\n    },\n\n    // @returns Parsley field computed value that could be overrided or configured in DOM\n    getValue: function () {\n      var value;\n\n      // Value could be overriden in DOM or with explicit options\n      if ('function' === typeof this.options.value)\n        value = this.options.value(this);\n      else if ('undefined' !== typeof this.options.value)\n        value = this.options.value;\n      else\n        value = this.$element.val();\n\n      // Handle wrong DOM or configurations\n      if ('undefined' === typeof value || null === value)\n        return '';\n\n      return this._handleWhitespace(value);\n    },\n\n    // Actualize options that could have change since previous validation\n    // Re-bind accordingly constraints (could be some new, removed or updated)\n    refreshConstraints: function () {\n      return this.actualizeOptions()._bindConstraints();\n    },\n\n    /**\n    * Add a new constraint to a field\n    *\n    * @param {String}   name\n    * @param {Mixed}    requirements      optional\n    * @param {Number}   priority          optional\n    * @param {Boolean}  isDomConstraint   optional\n    */\n    addConstraint: function (name, requirements, priority, isDomConstraint) {\n\n      if (window.Parsley._validatorRegistry.validators[name]) {\n        var constraint = new ConstraintFactory(this, name, requirements, priority, isDomConstraint);\n\n        // if constraint already exist, delete it and push new version\n        if ('undefined' !== this.constraintsByName[constraint.name])\n          this.removeConstraint(constraint.name);\n\n        this.constraints.push(constraint);\n        this.constraintsByName[constraint.name] = constraint;\n      }\n\n      return this;\n    },\n\n    // Remove a constraint\n    removeConstraint: function (name) {\n      for (var i = 0; i < this.constraints.length; i++)\n        if (name === this.constraints[i].name) {\n          this.constraints.splice(i, 1);\n          break;\n        }\n      delete this.constraintsByName[name];\n      return this;\n    },\n\n    // Update a constraint (Remove + re-add)\n    updateConstraint: function (name, parameters, priority) {\n      return this.removeConstraint(name)\n        .addConstraint(name, parameters, priority);\n    },\n\n    // # Internals\n\n    // Internal only.\n    // Bind constraints from config + options + DOM\n    _bindConstraints: function () {\n      var constraints = [];\n      var constraintsByName = {};\n\n      // clean all existing DOM constraints to only keep javascript user constraints\n      for (var i = 0; i < this.constraints.length; i++)\n        if (false === this.constraints[i].isDomConstraint) {\n          constraints.push(this.constraints[i]);\n          constraintsByName[this.constraints[i].name] = this.constraints[i];\n        }\n\n      this.constraints = constraints;\n      this.constraintsByName = constraintsByName;\n\n      // then re-add Parsley DOM-API constraints\n      for (var name in this.options)\n        this.addConstraint(name, this.options[name], undefined, true);\n\n      // finally, bind special HTML5 constraints\n      return this._bindHtml5Constraints();\n    },\n\n    // Internal only.\n    // Bind specific HTML5 constraints to be HTML5 compliant\n    _bindHtml5Constraints: function () {\n      // html5 required\n      if (this.$element.hasClass('required') || this.$element.attr('required'))\n        this.addConstraint('required', true, undefined, true);\n\n      // html5 pattern\n      if ('string' === typeof this.$element.attr('pattern'))\n        this.addConstraint('pattern', this.$element.attr('pattern'), undefined, true);\n\n      // range\n      if ('undefined' !== typeof this.$element.attr('min') && 'undefined' !== typeof this.$element.attr('max'))\n        this.addConstraint('range', [this.$element.attr('min'), this.$element.attr('max')], undefined, true);\n\n      // HTML5 min\n      else if ('undefined' !== typeof this.$element.attr('min'))\n        this.addConstraint('min', this.$element.attr('min'), undefined, true);\n\n      // HTML5 max\n      else if ('undefined' !== typeof this.$element.attr('max'))\n        this.addConstraint('max', this.$element.attr('max'), undefined, true);\n\n\n      // length\n      if ('undefined' !== typeof this.$element.attr('minlength') && 'undefined' !== typeof this.$element.attr('maxlength'))\n        this.addConstraint('length', [this.$element.attr('minlength'), this.$element.attr('maxlength')], undefined, true);\n\n      // HTML5 minlength\n      else if ('undefined' !== typeof this.$element.attr('minlength'))\n        this.addConstraint('minlength', this.$element.attr('minlength'), undefined, true);\n\n      // HTML5 maxlength\n      else if ('undefined' !== typeof this.$element.attr('maxlength'))\n        this.addConstraint('maxlength', this.$element.attr('maxlength'), undefined, true);\n\n\n      // html5 types\n      var type = this.$element.attr('type');\n\n      if ('undefined' === typeof type)\n        return this;\n\n      // Small special case here for HTML5 number: integer validator if step attribute is undefined or an integer value, number otherwise\n      if ('number' === type) {\n        return this.addConstraint('type', ['number', {\n          step: this.$element.attr('step'),\n          base: this.$element.attr('min') || this.$element.attr('value')\n        }], undefined, true);\n      // Regular other HTML5 supported types\n      } else if (/^(email|url|range)$/i.test(type)) {\n        return this.addConstraint('type', type, undefined, true);\n      }\n      return this;\n    },\n\n    // Internal only.\n    // Field is required if have required constraint without `false` value\n    _isRequired: function () {\n      if ('undefined' === typeof this.constraintsByName.required)\n        return false;\n\n      return false !== this.constraintsByName.required.requirements;\n    },\n\n    // Internal only.\n    // Shortcut to trigger an event\n    _trigger: function (eventName) {\n      return this.trigger('field:' + eventName);\n    },\n\n    // Internal only\n    // Handles whitespace in a value\n    // Use `data-parsley-whitespace=\"squish\"` to auto squish input value\n    // Use `data-parsley-whitespace=\"trim\"` to auto trim input value\n    _handleWhitespace: function (value) {\n      if (true === this.options.trimValue)\n        ParsleyUtils__default.warnOnce('data-parsley-trim-value=\"true\" is deprecated, please use data-parsley-whitespace=\"trim\"');\n\n      if ('squish' === this.options.whitespace)\n        value = value.replace(/\\s{2,}/g, ' ');\n\n      if (('trim' === this.options.whitespace) || ('squish' === this.options.whitespace) || (true === this.options.trimValue))\n        value = ParsleyUtils__default.trimString(value);\n\n      return value;\n    },\n\n    // Internal only.\n    // Returns the constraints, grouped by descending priority.\n    // The result is thus an array of arrays of constraints.\n    _getGroupedConstraints: function () {\n      if (false === this.options.priorityEnabled)\n        return [this.constraints];\n\n      var groupedConstraints = [];\n      var index = {};\n\n      // Create array unique of priorities\n      for (var i = 0; i < this.constraints.length; i++) {\n        var p = this.constraints[i].priority;\n        if (!index[p])\n          groupedConstraints.push(index[p] = []);\n        index[p].push(this.constraints[i]);\n      }\n      // Sort them by priority DESC\n      groupedConstraints.sort(function (a, b) { return b[0].priority - a[0].priority; });\n\n      return groupedConstraints;\n    }\n\n  };\n\n  var parsley_field = ParsleyField;\n\n  var ParsleyMultiple = function () {\n    this.__class__ = 'ParsleyFieldMultiple';\n  };\n\n  ParsleyMultiple.prototype = {\n    // Add new `$element` sibling for multiple field\n    addElement: function ($element) {\n      this.$elements.push($element);\n\n      return this;\n    },\n\n    // See `ParsleyField.refreshConstraints()`\n    refreshConstraints: function () {\n      var fieldConstraints;\n\n      this.constraints = [];\n\n      // Select multiple special treatment\n      if (this.$element.is('select')) {\n        this.actualizeOptions()._bindConstraints();\n\n        return this;\n      }\n\n      // Gather all constraints for each input in the multiple group\n      for (var i = 0; i < this.$elements.length; i++) {\n\n        // Check if element have not been dynamically removed since last binding\n        if (!$('html').has(this.$elements[i]).length) {\n          this.$elements.splice(i, 1);\n          continue;\n        }\n\n        fieldConstraints = this.$elements[i].data('ParsleyFieldMultiple').refreshConstraints().constraints;\n\n        for (var j = 0; j < fieldConstraints.length; j++)\n          this.addConstraint(fieldConstraints[j].name, fieldConstraints[j].requirements, fieldConstraints[j].priority, fieldConstraints[j].isDomConstraint);\n      }\n\n      return this;\n    },\n\n    // See `ParsleyField.getValue()`\n    getValue: function () {\n      // Value could be overriden in DOM\n      if ('function' === typeof this.options.value)\n        value = this.options.value(this);\n      else if ('undefined' !== typeof this.options.value)\n        return this.options.value;\n\n      // Radio input case\n      if (this.$element.is('input[type=radio]'))\n        return this._findRelated().filter(':checked').val() || '';\n\n      // checkbox input case\n      if (this.$element.is('input[type=checkbox]')) {\n        var values = [];\n\n        this._findRelated().filter(':checked').each(function () {\n          values.push($(this).val());\n        });\n\n        return values;\n      }\n\n      // Select multiple case\n      if (this.$element.is('select') && null === this.$element.val())\n        return [];\n\n      // Default case that should never happen\n      return this.$element.val();\n    },\n\n    _init: function () {\n      this.$elements = [this.$element];\n\n      return this;\n    }\n  };\n\n  var ParsleyFactory = function (element, options, parsleyFormInstance) {\n    this.$element = $(element);\n\n    // If the element has already been bound, returns its saved Parsley instance\n    var savedparsleyFormInstance = this.$element.data('Parsley');\n    if (savedparsleyFormInstance) {\n\n      // If the saved instance has been bound without a ParsleyForm parent and there is one given in this call, add it\n      if ('undefined' !== typeof parsleyFormInstance && savedparsleyFormInstance.parent === window.Parsley) {\n        savedparsleyFormInstance.parent = parsleyFormInstance;\n        savedparsleyFormInstance._resetOptions(savedparsleyFormInstance.options);\n      }\n\n      return savedparsleyFormInstance;\n    }\n\n    // Parsley must be instantiated with a DOM element or jQuery $element\n    if (!this.$element.length)\n      throw new Error('You must bind Parsley on an existing element.');\n\n    if ('undefined' !== typeof parsleyFormInstance && 'ParsleyForm' !== parsleyFormInstance.__class__)\n      throw new Error('Parent instance must be a ParsleyForm instance');\n\n    this.parent = parsleyFormInstance || window.Parsley;\n    return this.init(options);\n  };\n\n  ParsleyFactory.prototype = {\n    init: function (options) {\n      this.__class__ = 'Parsley';\n      this.__version__ = '2.3.5';\n      this.__id__ = ParsleyUtils__default.generateID();\n\n      // Pre-compute options\n      this._resetOptions(options);\n\n      // A ParsleyForm instance is obviously a `<form>` element but also every node that is not an input and has the `data-parsley-validate` attribute\n      if (this.$element.is('form') || (ParsleyUtils__default.checkAttr(this.$element, this.options.namespace, 'validate') && !this.$element.is(this.options.inputs)))\n        return this.bind('parsleyForm');\n\n      // Every other element is bound as a `ParsleyField` or `ParsleyFieldMultiple`\n      return this.isMultiple() ? this.handleMultiple() : this.bind('parsleyField');\n    },\n\n    isMultiple: function () {\n      return (this.$element.is('input[type=radio], input[type=checkbox]')) || (this.$element.is('select') && 'undefined' !== typeof this.$element.attr('multiple'));\n    },\n\n    // Multiples fields are a real nightmare :(\n    // Maybe some refactoring would be appreciated here...\n    handleMultiple: function () {\n      var name;\n      var multiple;\n      var parsleyMultipleInstance;\n\n      // Handle multiple name\n      if (this.options.multiple)\n        ; // We already have our 'multiple' identifier\n      else if ('undefined' !== typeof this.$element.attr('name') && this.$element.attr('name').length)\n        this.options.multiple = name = this.$element.attr('name');\n      else if ('undefined' !== typeof this.$element.attr('id') && this.$element.attr('id').length)\n        this.options.multiple = this.$element.attr('id');\n\n      // Special select multiple input\n      if (this.$element.is('select') && 'undefined' !== typeof this.$element.attr('multiple')) {\n        this.options.multiple = this.options.multiple || this.__id__;\n        return this.bind('parsleyFieldMultiple');\n\n      // Else for radio / checkboxes, we need a `name` or `data-parsley-multiple` to properly bind it\n      } else if (!this.options.multiple) {\n        ParsleyUtils__default.warn('To be bound by Parsley, a radio, a checkbox and a multiple select input must have either a name or a multiple option.', this.$element);\n        return this;\n      }\n\n      // Remove special chars\n      this.options.multiple = this.options.multiple.replace(/(:|\\.|\\[|\\]|\\{|\\}|\\$)/g, '');\n\n      // Add proper `data-parsley-multiple` to siblings if we have a valid multiple name\n      if ('undefined' !== typeof name) {\n        $('input[name=\"' + name + '\"]').each((i, input) => {\n          if ($(input).is('input[type=radio], input[type=checkbox]'))\n            $(input).attr(this.options.namespace + 'multiple', this.options.multiple);\n        });\n      }\n\n      // Check here if we don't already have a related multiple instance saved\n      var $previouslyRelated = this._findRelated();\n      for (var i = 0; i < $previouslyRelated.length; i++) {\n        parsleyMultipleInstance = $($previouslyRelated.get(i)).data('Parsley');\n        if ('undefined' !== typeof parsleyMultipleInstance) {\n\n          if (!this.$element.data('ParsleyFieldMultiple')) {\n            parsleyMultipleInstance.addElement(this.$element);\n          }\n\n          break;\n        }\n      }\n\n      // Create a secret ParsleyField instance for every multiple field. It will be stored in `data('ParsleyFieldMultiple')`\n      // And will be useful later to access classic `ParsleyField` stuff while being in a `ParsleyFieldMultiple` instance\n      this.bind('parsleyField', true);\n\n      return parsleyMultipleInstance || this.bind('parsleyFieldMultiple');\n    },\n\n    // Return proper `ParsleyForm`, `ParsleyField` or `ParsleyFieldMultiple`\n    bind: function (type, doNotStore) {\n      var parsleyInstance;\n\n      switch (type) {\n        case 'parsleyForm':\n          parsleyInstance = $.extend(\n            new ParsleyForm(this.$element, this.domOptions, this.options),\n            window.ParsleyExtend\n          )._bindFields();\n          break;\n        case 'parsleyField':\n          parsleyInstance = $.extend(\n            new parsley_field(this.$element, this.domOptions, this.options, this.parent),\n            window.ParsleyExtend\n          );\n          break;\n        case 'parsleyFieldMultiple':\n          parsleyInstance = $.extend(\n            new parsley_field(this.$element, this.domOptions, this.options, this.parent),\n            new ParsleyMultiple(),\n            window.ParsleyExtend\n          )._init();\n          break;\n        default:\n          throw new Error(type + 'is not a supported Parsley type');\n      }\n\n      if (this.options.multiple)\n        ParsleyUtils__default.setAttr(this.$element, this.options.namespace, 'multiple', this.options.multiple);\n\n      if ('undefined' !== typeof doNotStore) {\n        this.$element.data('ParsleyFieldMultiple', parsleyInstance);\n\n        return parsleyInstance;\n      }\n\n      // Store the freshly bound instance in a DOM element for later access using jQuery `data()`\n      this.$element.data('Parsley', parsleyInstance);\n\n      // Tell the world we have a new ParsleyForm or ParsleyField instance!\n      parsleyInstance._actualizeTriggers();\n      parsleyInstance._trigger('init');\n\n      return parsleyInstance;\n    }\n  };\n\n  var vernums = $.fn.jquery.split('.');\n  if (parseInt(vernums[0]) <= 1 && parseInt(vernums[1]) < 8) {\n    throw \"The loaded version of jQuery is too old. Please upgrade to 1.8.x or better.\";\n  }\n  if (!vernums.forEach) {\n    ParsleyUtils__default.warn('Parsley requires ES5 to run properly. Please include https://github.com/es-shims/es5-shim');\n  }\n  // Inherit `on`, `off` & `trigger` to Parsley:\n  var Parsley = $.extend(new ParsleyAbstract(), {\n      $element: $(document),\n      actualizeOptions: null,\n      _resetOptions: null,\n      Factory: ParsleyFactory,\n      version: '2.3.5'\n    });\n\n  // Supplement ParsleyField and Form with ParsleyAbstract\n  // This way, the constructors will have access to those methods\n  $.extend(parsley_field.prototype, ParsleyUI.Field, ParsleyAbstract.prototype);\n  $.extend(ParsleyForm.prototype, ParsleyUI.Form, ParsleyAbstract.prototype);\n  // Inherit actualizeOptions and _resetOptions:\n  $.extend(ParsleyFactory.prototype, ParsleyAbstract.prototype);\n\n  // ### jQuery API\n  // `$('.elem').parsley(options)` or `$('.elem').psly(options)`\n  $.fn.parsley = $.fn.psly = function (options) {\n    if (this.length > 1) {\n      var instances = [];\n\n      this.each(function () {\n        instances.push($(this).parsley(options));\n      });\n\n      return instances;\n    }\n\n    // Return undefined if applied to non existing DOM element\n    if (!$(this).length) {\n      ParsleyUtils__default.warn('You must bind Parsley on an existing element.');\n\n      return;\n    }\n\n    return new ParsleyFactory(this, options);\n  };\n\n  // ### ParsleyField and ParsleyForm extension\n  // Ensure the extension is now defined if it wasn't previously\n  if ('undefined' === typeof window.ParsleyExtend)\n    window.ParsleyExtend = {};\n\n  // ### Parsley config\n  // Inherit from ParsleyDefault, and copy over any existing values\n  Parsley.options = $.extend(ParsleyUtils__default.objectCreate(ParsleyDefaults), window.ParsleyConfig);\n  window.ParsleyConfig = Parsley.options; // Old way of accessing global options\n\n  // ### Globals\n  window.Parsley = window.psly = Parsley;\n  window.ParsleyUtils = ParsleyUtils__default;\n\n  // ### Define methods that forward to the registry, and deprecate all access except through window.Parsley\n  var registry = window.Parsley._validatorRegistry = new ParsleyValidatorRegistry(window.ParsleyConfig.validators, window.ParsleyConfig.i18n);\n  window.ParsleyValidator = {};\n  $.each('setLocale addCatalog addMessage addMessages getErrorMessage formatMessage addValidator updateValidator removeValidator'.split(' '), function (i, method) {\n    window.Parsley[method] = $.proxy(registry, method);\n    window.ParsleyValidator[method] = function () {\n      ParsleyUtils__default.warnOnce(`Accessing the method '${method}' through ParsleyValidator is deprecated. Simply call 'window.Parsley.${method}(...)'`);\n      return window.Parsley[method](...arguments);\n    };\n  });\n\n  // ### ParsleyUI\n  // Deprecated global object\n  window.Parsley.UI = ParsleyUI;\n  window.ParsleyUI = {\n    removeError: function (instance, name, doNotUpdateClass) {\n      var updateClass = true !== doNotUpdateClass;\n      ParsleyUtils__default.warnOnce(`Accessing ParsleyUI is deprecated. Call 'removeError' on the instance directly. Please comment in issue 1073 as to your need to call this method.`);\n      return instance.removeError(name, {updateClass});\n    },\n    getErrorsMessages: function (instance) {\n      ParsleyUtils__default.warnOnce(`Accessing ParsleyUI is deprecated. Call 'getErrorsMessages' on the instance directly.`);\n      return instance.getErrorsMessages();\n    }\n  };\n  $.each('addError updateError'.split(' '), function (i, method) {\n    window.ParsleyUI[method] = function (instance, name, message, assert, doNotUpdateClass) {\n      var updateClass = true !== doNotUpdateClass;\n      ParsleyUtils__default.warnOnce(`Accessing ParsleyUI is deprecated. Call '${method}' on the instance directly. Please comment in issue 1073 as to your need to call this method.`);\n      return instance[method](name, {message, assert, updateClass});\n    };\n  });\n\n  // Alleviate glaring Firefox bug https://bugzilla.mozilla.org/show_bug.cgi?id=1250521\n  // See also https://github.com/guillaumepotier/Parsley.js/issues/1068\n  if (/firefox/i.test(navigator.userAgent)) {\n    $(document).on('change', 'select', evt => {\n      $(evt.target).trigger('input');\n    });\n  }\n\n  // ### PARSLEY auto-binding\n  // Prevent it by setting `ParsleyConfig.autoBind` to `false`\n  if (false !== window.ParsleyConfig.autoBind) {\n    $(function () {\n      // Works only on `data-parsley-validate`.\n      if ($('[data-parsley-validate]').length)\n        $('[data-parsley-validate]').parsley();\n    });\n  }\n\n  var o = $({});\n  var deprecated = function () {\n    ParsleyUtils__default.warnOnce(\"Parsley's pubsub module is deprecated; use the 'on' and 'off' methods on parsley instances or window.Parsley\");\n  };\n\n  // Returns an event handler that calls `fn` with the arguments it expects\n  function adapt(fn, context) {\n    // Store to allow unbinding\n    if (!fn.parsleyAdaptedCallback) {\n      fn.parsleyAdaptedCallback = function () {\n        var args = Array.prototype.slice.call(arguments, 0);\n        args.unshift(this);\n        fn.apply(context || o, args);\n      };\n    }\n    return fn.parsleyAdaptedCallback;\n  }\n\n  var eventPrefix = 'parsley:';\n  // Converts 'parsley:form:validate' into 'form:validate'\n  function eventName(name) {\n    if (name.lastIndexOf(eventPrefix, 0) === 0)\n      return name.substr(eventPrefix.length);\n    return name;\n  }\n\n  // $.listen is deprecated. Use Parsley.on instead.\n  $.listen = function (name, callback) {\n    var context;\n    deprecated();\n    if ('object' === typeof arguments[1] && 'function' === typeof arguments[2]) {\n      context = arguments[1];\n      callback = arguments[2];\n    }\n\n    if ('function' !== typeof callback)\n      throw new Error('Wrong parameters');\n\n    window.Parsley.on(eventName(name), adapt(callback, context));\n  };\n\n  $.listenTo = function (instance, name, fn) {\n    deprecated();\n    if (!(instance instanceof parsley_field) && !(instance instanceof ParsleyForm))\n      throw new Error('Must give Parsley instance');\n\n    if ('string' !== typeof name || 'function' !== typeof fn)\n      throw new Error('Wrong parameters');\n\n    instance.on(eventName(name), adapt(fn));\n  };\n\n  $.unsubscribe = function (name, fn) {\n    deprecated();\n    if ('string' !== typeof name || 'function' !== typeof fn)\n      throw new Error('Wrong arguments');\n    window.Parsley.off(eventName(name), fn.parsleyAdaptedCallback);\n  };\n\n  $.unsubscribeTo = function (instance, name) {\n    deprecated();\n    if (!(instance instanceof parsley_field) && !(instance instanceof ParsleyForm))\n      throw new Error('Must give Parsley instance');\n    instance.off(eventName(name));\n  };\n\n  $.unsubscribeAll = function (name) {\n    deprecated();\n    window.Parsley.off(eventName(name));\n    $('form,input,textarea,select').each(function () {\n      var instance = $(this).data('Parsley');\n      if (instance) {\n        instance.off(eventName(name));\n      }\n    });\n  };\n\n  // $.emit is deprecated. Use jQuery events instead.\n  $.emit = function (name, instance) {\n    deprecated();\n    var instanceGiven = (instance instanceof parsley_field) || (instance instanceof ParsleyForm);\n    var args = Array.prototype.slice.call(arguments, instanceGiven ? 2 : 1);\n    args.unshift(eventName(name));\n    if (!instanceGiven) {\n      instance = window.Parsley;\n    }\n    instance.trigger(...args);\n  };\n\n  var pubsub = {};\n\n  $.extend(true, Parsley, {\n    asyncValidators: {\n      'default': {\n        fn: function (xhr) {\n          // By default, only status 2xx are deemed successful.\n          // Note: we use status instead of state() because responses with status 200\n          // but invalid messages (e.g. an empty body for content type set to JSON) will\n          // result in state() === 'rejected'.\n          return xhr.status >= 200 && xhr.status < 300;\n        },\n        url: false\n      },\n      reverse: {\n        fn: function (xhr) {\n          // If reverse option is set, a failing ajax request is considered successful\n          return xhr.status < 200 || xhr.status >= 300;\n        },\n        url: false\n      }\n    },\n\n    addAsyncValidator: function (name, fn, url, options) {\n      Parsley.asyncValidators[name] = {\n        fn: fn,\n        url: url || false,\n        options: options || {}\n      };\n\n      return this;\n    }\n\n  });\n\n  Parsley.addValidator('remote', {\n    requirementType: {\n      '': 'string',\n      'validator': 'string',\n      'reverse': 'boolean',\n      'options': 'object'\n    },\n\n    validateString: function (value, url, options, instance) {\n      var data = {};\n      var ajaxOptions;\n      var csr;\n      var validator = options.validator || (true === options.reverse ? 'reverse' : 'default');\n\n      if ('undefined' === typeof Parsley.asyncValidators[validator])\n        throw new Error('Calling an undefined async validator: `' + validator + '`');\n\n      url = Parsley.asyncValidators[validator].url || url;\n\n      // Fill current value\n      if (url.indexOf('{value}') > -1) {\n        url = url.replace('{value}', encodeURIComponent(value));\n      } else {\n        data[instance.$element.attr('name') || instance.$element.attr('id')] = value;\n      }\n\n      // Merge options passed in from the function with the ones in the attribute\n      var remoteOptions = $.extend(true, options.options || {} , Parsley.asyncValidators[validator].options);\n\n      // All `$.ajax(options)` could be overridden or extended directly from DOM in `data-parsley-remote-options`\n      ajaxOptions = $.extend(true, {}, {\n        url: url,\n        data: data,\n        type: 'GET'\n      }, remoteOptions);\n\n      // Generate store key based on ajax options\n      instance.trigger('field:ajaxoptions', instance, ajaxOptions);\n\n      csr = $.param(ajaxOptions);\n\n      // Initialise querry cache\n      if ('undefined' === typeof Parsley._remoteCache)\n        Parsley._remoteCache = {};\n\n      // Try to retrieve stored xhr\n      var xhr = Parsley._remoteCache[csr] = Parsley._remoteCache[csr] || $.ajax(ajaxOptions);\n\n      var handleXhr = function () {\n        var result = Parsley.asyncValidators[validator].fn.call(instance, xhr, url, options);\n        if (!result) // Map falsy results to rejected promise\n          result = $.Deferred().reject();\n        return $.when(result);\n      };\n\n      return xhr.then(handleXhr, handleXhr);\n    },\n\n    priority: -1\n  });\n\n  Parsley.on('form:submit', function () {\n    Parsley._remoteCache = {};\n  });\n\n  window.ParsleyExtend.addAsyncValidator = function () {\n    ParsleyUtils.warnOnce('Accessing the method `addAsyncValidator` through an instance is deprecated. Simply call `Parsley.addAsyncValidator(...)`');\n    return Parsley.addAsyncValidator(...arguments);\n  };\n\n  // This is included with the Parsley library itself,\n  // thus there is no use in adding it to your project.\n  Parsley.addMessages('en', {\n    defaultMessage: \"This value seems to be invalid.\",\n    type: {\n      email:        \"This value should be a valid email.\",\n      url:          \"This value should be a valid url.\",\n      number:       \"This value should be a valid number.\",\n      integer:      \"This value should be a valid integer.\",\n      digits:       \"This value should be digits.\",\n      alphanum:     \"This value should be alphanumeric.\"\n    },\n    notblank:       \"This value should not be blank.\",\n    required:       \"This value is required.\",\n    pattern:        \"This value seems to be invalid.\",\n    min:            \"This value should be greater than or equal to %s.\",\n    max:            \"This value should be lower than or equal to %s.\",\n    range:          \"This value should be between %s and %s.\",\n    minlength:      \"This value is too short. It should have %s characters or more.\",\n    maxlength:      \"This value is too long. It should have %s characters or fewer.\",\n    length:         \"This value length is invalid. It should be between %s and %s characters long.\",\n    mincheck:       \"You must select at least %s choices.\",\n    maxcheck:       \"You must select %s choices or fewer.\",\n    check:          \"You must select between %s and %s choices.\",\n    equalto:        \"This value should be the same.\"\n  });\n\n  Parsley.setLocale('en');\n\n  var parsley = Parsley;\n\n  return parsley;\n\n}));\n", "import $ from 'jquery';\nimport Parsley<PERSON>ield from './field';\nimport ParsleyForm from './form';\nimport ParsleyUtils from './utils';\n\nvar o = $({});\nvar deprecated = function () {\n  ParsleyUtils.warnOnce(\"Parsley's pubsub module is deprecated; use the 'on' and 'off' methods on parsley instances or window.Parsley\");\n};\n\n// Returns an event handler that calls `fn` with the arguments it expects\nfunction adapt(fn, context) {\n  // Store to allow unbinding\n  if (!fn.parsleyAdaptedCallback) {\n    fn.parsleyAdaptedCallback = function () {\n      var args = Array.prototype.slice.call(arguments, 0);\n      args.unshift(this);\n      fn.apply(context || o, args);\n    };\n  }\n  return fn.parsleyAdaptedCallback;\n}\n\nvar eventPrefix = 'parsley:';\n// Converts 'parsley:form:validate' into 'form:validate'\nfunction eventName(name) {\n  if (name.lastIndexOf(eventPrefix, 0) === 0)\n    return name.substr(eventPrefix.length);\n  return name;\n}\n\n// $.listen is deprecated. Use Parsley.on instead.\n$.listen = function (name, callback) {\n  var context;\n  deprecated();\n  if ('object' === typeof arguments[1] && 'function' === typeof arguments[2]) {\n    context = arguments[1];\n    callback = arguments[2];\n  }\n\n  if ('function' !== typeof callback)\n    throw new Error('Wrong parameters');\n\n  window.Parsley.on(eventName(name), adapt(callback, context));\n};\n\n$.listenTo = function (instance, name, fn) {\n  deprecated();\n  if (!(instance instanceof ParsleyField) && !(instance instanceof ParsleyForm))\n    throw new Error('Must give Parsley instance');\n\n  if ('string' !== typeof name || 'function' !== typeof fn)\n    throw new Error('Wrong parameters');\n\n  instance.on(eventName(name), adapt(fn));\n};\n\n$.unsubscribe = function (name, fn) {\n  deprecated();\n  if ('string' !== typeof name || 'function' !== typeof fn)\n    throw new Error('Wrong arguments');\n  window.Parsley.off(eventName(name), fn.parsleyAdaptedCallback);\n};\n\n$.unsubscribeTo = function (instance, name) {\n  deprecated();\n  if (!(instance instanceof ParsleyField) && !(instance instanceof ParsleyForm))\n    throw new Error('Must give Parsley instance');\n  instance.off(eventName(name));\n};\n\n$.unsubscribeAll = function (name) {\n  deprecated();\n  window.Parsley.off(eventName(name));\n  $('form,input,textarea,select').each(function () {\n    var instance = $(this).data('Parsley');\n    if (instance) {\n      instance.off(eventName(name));\n    }\n  });\n};\n\n// $.emit is deprecated. Use jQuery events instead.\n$.emit = function (name, instance) {\n  deprecated();\n  var instanceGiven = (instance instanceof ParsleyField) || (instance instanceof ParsleyForm);\n  var args = Array.prototype.slice.call(arguments, instanceGiven ? 2 : 1);\n  args.unshift(eventName(name));\n  if (!instanceGiven) {\n    instance = window.Parsley;\n  }\n  instance.trigger(...args);\n};\n\nexport default {};\n", "import $ from 'jquery';\n\nvar globalID = 1;\nvar pastWarnings = {};\n\nvar ParsleyUtils = {\n  // Parsley DOM-API\n  // returns object from dom attributes and values\n  attr: function ($element, namespace, obj) {\n    var i;\n    var attribute;\n    var attributes;\n    var regex = new RegExp('^' + namespace, 'i');\n\n    if ('undefined' === typeof obj)\n      obj = {};\n    else {\n      // Clear all own properties. This won't affect prototype's values\n      for (i in obj) {\n        if (obj.hasOwnProperty(i))\n          delete obj[i];\n      }\n    }\n\n    if ('undefined' === typeof $element || 'undefined' === typeof $element[0])\n      return obj;\n\n    attributes = $element[0].attributes;\n    for (i = attributes.length; i--; ) {\n      attribute = attributes[i];\n\n      if (attribute && attribute.specified && regex.test(attribute.name)) {\n        obj[this.camelize(attribute.name.slice(namespace.length))] = this.deserializeValue(attribute.value);\n      }\n    }\n\n    return obj;\n  },\n\n  checkAttr: function ($element, namespace, checkAttr) {\n    return $element.is('[' + namespace + checkAttr + ']');\n  },\n\n  setAttr: function ($element, namespace, attr, value) {\n    $element[0].setAttribute(this.dasherize(namespace + attr), String(value));\n  },\n\n  generateID: function () {\n    return '' + globalID++;\n  },\n\n  /** Third party functions **/\n  // Zepto deserialize function\n  deserializeValue: function (value) {\n    var num;\n\n    try {\n      return value ?\n        value == \"true\" ||\n        (value == \"false\" ? false :\n        value == \"null\" ? null :\n        !isNaN(num = Number(value)) ? num :\n        /^[\\[\\{]/.test(value) ? $.parseJSON(value) :\n        value)\n        : value;\n    } catch (e) { return value; }\n  },\n\n  // Zepto camelize function\n  camelize: function (str) {\n    return str.replace(/-+(.)?/g, function (match, chr) {\n      return chr ? chr.toUpperCase() : '';\n    });\n  },\n\n  // Zepto dasherize function\n  dasherize: function (str) {\n    return str.replace(/::/g, '/')\n      .replace(/([A-Z]+)([A-Z][a-z])/g, '$1_$2')\n      .replace(/([a-z\\d])([A-Z])/g, '$1_$2')\n      .replace(/_/g, '-')\n      .toLowerCase();\n  },\n\n  warn: function () {\n    if (window.console && 'function' === typeof window.console.warn)\n      window.console.warn(...arguments);\n  },\n\n  warnOnce: function(msg) {\n    if (!pastWarnings[msg]) {\n      pastWarnings[msg] = true;\n      this.warn(...arguments);\n    }\n  },\n\n  _resetWarnings: function () {\n    pastWarnings = {};\n  },\n\n  trimString: function(string) {\n    return string.replace(/^\\s+|\\s+$/g, '');\n  },\n\n  namespaceEvents: function(events, namespace) {\n    events = this.trimString(events || '').split(/\\s+/);\n    if (!events[0])\n      return '';\n    return $.map(events, evt => { return `${evt}.${namespace}`; }).join(' ');\n  },\n\n  // Object.create polyfill, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/create#Polyfill\n  objectCreate: Object.create || (function () {\n    var Object = function () {};\n    return function (prototype) {\n      if (arguments.length > 1) {\n        throw Error('Second argument not supported');\n      }\n      if (typeof prototype != 'object') {\n        throw TypeError('Argument must be an object');\n      }\n      Object.prototype = prototype;\n      var result = new Object();\n      Object.prototype = null;\n      return result;\n    };\n  })()\n};\n\nexport default ParsleyUtils;\n", "// All these options could be overriden and specified directly in DOM using\n// `data-parsley-` default DOM-API\n// eg: `inputs` can be set in DOM using `data-parsley-inputs=\"input, textarea\"`\n// eg: `data-parsley-stop-on-first-failing-constraint=\"false\"`\n\nvar ParsleyDefaults = {\n  // ### General\n\n  // Default data-namespace for DOM API\n  namespace: 'data-parsley-',\n\n  // Supported inputs by default\n  inputs: 'input, textarea, select',\n\n  // Excluded inputs by default\n  excluded: 'input[type=button], input[type=submit], input[type=reset], input[type=hidden]',\n\n  // Stop validating field on highest priority failing constraint\n  priorityEnabled: true,\n\n  // ### Field only\n\n  // identifier used to group together inputs (e.g. radio buttons...)\n  multiple: null,\n\n  // identifier (or array of identifiers) used to validate only a select group of inputs\n  group: null,\n\n  // ### UI\n  // Enable\\Disable error messages\n  uiEnabled: true,\n\n  // Key events threshold before validation\n  validationThreshold: 3,\n\n  // Focused field on form validation error. 'first'|'last'|'none'\n  focus: 'first',\n\n  // event(s) that will trigger validation before first failure. eg: `input`...\n  trigger: false,\n\n  // event(s) that will trigger validation after first failure.\n  triggerAfterFailure: 'input',\n\n  // Class that would be added on every failing validation Parsley field\n  errorClass: 'parsley-error',\n\n  // Same for success validation\n  successClass: 'parsley-success',\n\n  // Return the `$element` that will receive these above success or error classes\n  // Could also be (and given directly from DOM) a valid selector like `'#div'`\n  classHandler: function (ParsleyField) {},\n\n  // Return the `$element` where errors will be appended\n  // Could also be (and given directly from DOM) a valid selector like `'#div'`\n  errorsContainer: function (ParsleyField) {},\n\n  // ul elem that would receive errors' list\n  errorsWrapper: '<ul class=\"parsley-errors-list\"></ul>',\n\n  // li elem that would receive error message\n  errorTemplate: '<li></li>'\n};\n\nexport default ParsleyDefaults;\n", "import $ from 'jquery';\nimport ParsleyUtils from './utils';\n\nvar ParsleyAbstract = function () {};\n\nParsleyAbstract.prototype = {\n  asyncSupport: true, // Deprecated\n\n  actualizeOptions: function () {\n    ParsleyUtils.attr(this.$element, this.options.namespace, this.domOptions);\n    if (this.parent && this.parent.actualizeOptions)\n      this.parent.actualizeOptions();\n    return this;\n  },\n\n  _resetOptions: function (initOptions) {\n    this.domOptions = ParsleyUtils.objectCreate(this.parent.options);\n    this.options = ParsleyUtils.objectCreate(this.domOptions);\n    // Shallow copy of ownProperties of initOptions:\n    for (var i in initOptions) {\n      if (initOptions.hasOwnProperty(i))\n        this.options[i] = initOptions[i];\n    }\n    this.actualizeOptions();\n  },\n\n  _listeners: null,\n\n  // Register a callback for the given event name\n  // Callback is called with context as the first argument and the `this`\n  // The context is the current parsley instance, or window.Parsley if global\n  // A return value of `false` will interrupt the calls\n  on: function (name, fn) {\n    this._listeners = this._listeners || {};\n    var queue = this._listeners[name] = this._listeners[name] || [];\n    queue.push(fn);\n\n    return this;\n  },\n\n  // Deprecated. Use `on` instead\n  subscribe: function(name, fn) {\n    $.listenTo(this, name.toLowerCase(), fn);\n  },\n\n  // Unregister a callback (or all if none is given) for the given event name\n  off: function (name, fn) {\n    var queue = this._listeners && this._listeners[name];\n    if (queue) {\n      if (!fn) {\n        delete this._listeners[name];\n      } else {\n        for (var i = queue.length; i--; )\n          if (queue[i] === fn)\n            queue.splice(i, 1);\n      }\n    }\n    return this;\n  },\n\n  // Deprecated. Use `off`\n  unsubscribe: function(name, fn) {\n    $.unsubscribeTo(this, name.toLowerCase());\n  },\n\n  // Trigger an event of the given name\n  // A return value of `false` interrupts the callback chain\n  // Returns false if execution was interrupted\n  trigger: function (name, target, extraArg) {\n    target = target || this;\n    var queue = this._listeners && this._listeners[name];\n    var result;\n    var parentResult;\n    if (queue) {\n      for (var i = queue.length; i--; ) {\n        result = queue[i].call(target, target, extraArg);\n        if (result === false) return result;\n      }\n    }\n    if (this.parent) {\n      return this.parent.trigger(name, target, extraArg);\n    }\n    return true;\n  },\n\n  // Reset UI\n  reset: function () {\n    // Field case: just emit a reset event for UI\n    if ('ParsleyForm' !== this.__class__) {\n      this._resetUI();\n      return this._trigger('reset');\n    }\n\n    // Form case: emit a reset event for each field\n    for (var i = 0; i < this.fields.length; i++)\n      this.fields[i].reset();\n\n    this._trigger('reset');\n  },\n\n  // Destroy Parsley instance (+ UI)\n  destroy: function () {\n    // Field case: emit destroy event to clean UI and then destroy stored instance\n    this._destroyUI();\n    if ('ParsleyForm' !== this.__class__) {\n      this.$element.removeData('Parsley');\n      this.$element.removeData('ParsleyFieldMultiple');\n      this._trigger('destroy');\n\n      return;\n    }\n\n    // Form case: destroy all its fields and then destroy stored instance\n    for (var i = 0; i < this.fields.length; i++)\n      this.fields[i].destroy();\n\n    this.$element.removeData('Parsley');\n    this._trigger('destroy');\n  },\n\n  asyncIsValid: function (group, force) {\n    ParsleyUtils.warnOnce(\"asyncIsValid is deprecated; please use whenValid instead\");\n    return this.whenValid({group, force});\n  },\n\n  _findRelated: function () {\n    return this.options.multiple ?\n      this.parent.$element.find(`[${this.options.namespace}multiple=\"${this.options.multiple}\"]`)\n    : this.$element;\n  }\n};\n\nexport default ParsleyAbstract;\n", "import $ from 'jquery';\nimport ParsleyUtils from './utils';\n\nvar requirementConverters = {\n  string: function(string) {\n    return string;\n  },\n  integer: function(string) {\n    if (isNaN(string))\n      throw 'Requirement is not an integer: \"' + string + '\"';\n    return parseInt(string, 10);\n  },\n  number: function(string) {\n    if (isNaN(string))\n      throw 'Requirement is not a number: \"' + string + '\"';\n    return parseFloat(string);\n  },\n  reference: function(string) { // Unused for now\n    var result = $(string);\n    if (result.length === 0)\n      throw 'No such reference: \"' + string + '\"';\n    return result;\n  },\n  boolean: function(string) {\n    return string !== 'false';\n  },\n  object: function(string) {\n    return ParsleyUtils.deserializeValue(string);\n  },\n  regexp: function(regexp) {\n    var flags = '';\n\n    // Test if RegExp is literal, if not, nothing to be done, otherwise, we need to isolate flags and pattern\n    if (/^\\/.*\\/(?:[gimy]*)$/.test(regexp)) {\n      // Replace the regexp literal string with the first match group: ([gimy]*)\n      // If no flag is present, this will be a blank string\n      flags = regexp.replace(/.*\\/([gimy]*)$/, '$1');\n      // Again, replace the regexp literal string with the first match group:\n      // everything excluding the opening and closing slashes and the flags\n      regexp = regexp.replace(new RegExp('^/(.*?)/' + flags + '$'), '$1');\n    } else {\n      // Anchor regexp:\n      regexp = '^' + regexp + '$';\n    }\n    return new RegExp(regexp, flags);\n  }\n};\n\nvar convertArrayRequirement = function(string, length) {\n  var m = string.match(/^\\s*\\[(.*)\\]\\s*$/);\n  if (!m)\n    throw 'Requirement is not an array: \"' + string + '\"';\n  var values = m[1].split(',').map(ParsleyUtils.trimString);\n  if (values.length !== length)\n    throw 'Requirement has ' + values.length + ' values when ' + length + ' are needed';\n  return values;\n};\n\nvar convertRequirement = function(requirementType, string) {\n  var converter = requirementConverters[requirementType || 'string'];\n  if (!converter)\n    throw 'Unknown requirement specification: \"' + requirementType + '\"';\n  return converter(string);\n};\n\nvar convertExtraOptionRequirement = function(requirementSpec, string, extraOptionReader) {\n  var main = null;\n  var extra = {};\n  for (var key in requirementSpec) {\n    if (key) {\n      var value = extraOptionReader(key);\n      if ('string' === typeof value)\n        value = convertRequirement(requirementSpec[key], value);\n      extra[key] = value;\n    } else {\n      main = convertRequirement(requirementSpec[key], string);\n    }\n  }\n  return [main, extra];\n};\n\n// A Validator needs to implement the methods `validate` and `parseRequirements`\n\nvar ParsleyValidator = function(spec) {\n  $.extend(true, this, spec);\n};\n\nParsleyValidator.prototype = {\n  // Returns `true` iff the given `value` is valid according the given requirements.\n  validate: function(value, requirementFirstArg) {\n    if (this.fn) { // Legacy style validator\n\n      if (arguments.length > 3)  // If more args then value, requirement, instance...\n        requirementFirstArg = [].slice.call(arguments, 1, -1);  // Skip first arg (value) and last (instance), combining the rest\n      return this.fn.call(this, value, requirementFirstArg);\n    }\n\n    if ($.isArray(value)) {\n      if (!this.validateMultiple)\n        throw 'Validator `' + this.name + '` does not handle multiple values';\n      return this.validateMultiple(...arguments);\n    } else {\n      if (this.validateNumber) {\n        if (isNaN(value))\n          return false;\n        arguments[0] = parseFloat(arguments[0]);\n        return this.validateNumber(...arguments);\n      }\n      if (this.validateString) {\n        return this.validateString(...arguments);\n      }\n      throw 'Validator `' + this.name + '` only handles multiple values';\n    }\n  },\n\n  // Parses `requirements` into an array of arguments,\n  // according to `this.requirementType`\n  parseRequirements: function(requirements, extraOptionReader) {\n    if ('string' !== typeof requirements) {\n      // Assume requirement already parsed\n      // but make sure we return an array\n      return $.isArray(requirements) ? requirements : [requirements];\n    }\n    var type = this.requirementType;\n    if ($.isArray(type)) {\n      var values = convertArrayRequirement(requirements, type.length);\n      for (var i = 0; i < values.length; i++)\n        values[i] = convertRequirement(type[i], values[i]);\n      return values;\n    } else if ($.isPlainObject(type)) {\n      return convertExtraOptionRequirement(type, requirements, extraOptionReader);\n    } else {\n      return [convertRequirement(type, requirements)];\n    }\n  },\n  // Defaults:\n  requirementType: 'string',\n\n  priority: 2\n\n};\n\nexport default ParsleyValidator;\n", "import $ from 'jquery';\nimport ParsleyUtils from './utils';\nimport ParsleyDefaults from './defaults';\nimport ParsleyValidator from './validator';\n\nvar ParsleyValidatorRegistry = function (validators, catalog) {\n  this.__class__ = 'ParsleyValidatorRegistry';\n\n  // Default Parsley locale is en\n  this.locale = 'en';\n\n  this.init(validators || {}, catalog || {});\n};\n\nvar typeRegexes =  {\n  email: /^((([a-z]|\\d|[!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])+(\\.([a-z]|\\d|[!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])+)*)|((\\x22)((((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(([\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x7f]|\\x21|[\\x23-\\x5b]|[\\x5d-\\x7e]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(\\\\([\\x01-\\x09\\x0b\\x0c\\x0d-\\x7f]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]))))*(((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(\\x22)))@((([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.)+(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))$/i,\n\n  // Follow https://www.w3.org/TR/html5/infrastructure.html#floating-point-numbers\n  number: /^-?(\\d*\\.)?\\d+(e[-+]?\\d+)?$/i,\n\n  integer: /^-?\\d+$/,\n\n  digits: /^\\d+$/,\n\n  alphanum: /^\\w+$/i,\n\n  url: new RegExp(\n      \"^\" +\n        // protocol identifier\n        \"(?:(?:https?|ftp)://)?\" + // ** mod: make scheme optional\n        // user:pass authentication\n        \"(?:\\\\S+(?::\\\\S*)?@)?\" +\n        \"(?:\" +\n          // IP address exclusion\n          // private & local networks\n          // \"(?!(?:10|127)(?:\\\\.\\\\d{1,3}){3})\" +   // ** mod: allow local networks\n          // \"(?!(?:169\\\\.254|192\\\\.168)(?:\\\\.\\\\d{1,3}){2})\" +  // ** mod: allow local networks\n          // \"(?!172\\\\.(?:1[6-9]|2\\\\d|3[0-1])(?:\\\\.\\\\d{1,3}){2})\" +  // ** mod: allow local networks\n          // IP address dotted notation octets\n          // excludes loopback network 0.0.0.0\n          // excludes reserved space >= *********\n          // excludes network & broacast addresses\n          // (first & last IP address of each class)\n          \"(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])\" +\n          \"(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}\" +\n          \"(?:\\\\.(?:[1-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))\" +\n        \"|\" +\n          // host name\n          \"(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)\" +\n          // domain name\n          \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*\" +\n          // TLD identifier\n          \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))\" +\n        \")\" +\n        // port number\n        \"(?::\\\\d{2,5})?\" +\n        // resource path\n        \"(?:/\\\\S*)?\" +\n      \"$\", 'i'\n    )\n};\ntypeRegexes.range = typeRegexes.number;\n\n// See http://stackoverflow.com/a/10454560/8279\nvar decimalPlaces = num => {\n  var match = ('' + num).match(/(?:\\.(\\d+))?(?:[eE]([+-]?\\d+))?$/);\n  if (!match) { return 0; }\n  return Math.max(\n       0,\n       // Number of digits right of decimal point.\n       (match[1] ? match[1].length : 0) -\n       // Adjust for scientific notation.\n       (match[2] ? +match[2] : 0));\n};\n\nParsleyValidatorRegistry.prototype = {\n  init: function (validators, catalog) {\n    this.catalog = catalog;\n    // Copy prototype's validators:\n    this.validators = $.extend({}, this.validators);\n\n    for (var name in validators)\n      this.addValidator(name, validators[name].fn, validators[name].priority);\n\n    window.Parsley.trigger('parsley:validator:init');\n  },\n\n  // Set new messages locale if we have dictionary loaded in ParsleyConfig.i18n\n  setLocale: function (locale) {\n    if ('undefined' === typeof this.catalog[locale])\n      throw new Error(locale + ' is not available in the catalog');\n\n    this.locale = locale;\n\n    return this;\n  },\n\n  // Add a new messages catalog for a given locale. Set locale for this catalog if set === `true`\n  addCatalog: function (locale, messages, set) {\n    if ('object' === typeof messages)\n      this.catalog[locale] = messages;\n\n    if (true === set)\n      return this.setLocale(locale);\n\n    return this;\n  },\n\n  // Add a specific message for a given constraint in a given locale\n  addMessage: function (locale, name, message) {\n    if ('undefined' === typeof this.catalog[locale])\n      this.catalog[locale] = {};\n\n    this.catalog[locale][name] = message;\n\n    return this;\n  },\n\n  // Add messages for a given locale\n  addMessages: function (locale, nameMessageObject) {\n    for (var name in nameMessageObject)\n      this.addMessage(locale, name, nameMessageObject[name]);\n\n    return this;\n  },\n\n  // Add a new validator\n  //\n  //    addValidator('custom', {\n  //        requirementType: ['integer', 'integer'],\n  //        validateString: function(value, from, to) {},\n  //        priority: 22,\n  //        messages: {\n  //          en: \"Hey, that's no good\",\n  //          fr: \"Aye aye, pas bon du tout\",\n  //        }\n  //    })\n  //\n  // Old API was addValidator(name, function, priority)\n  //\n  addValidator: function (name, arg1, arg2) {\n    if (this.validators[name])\n      ParsleyUtils.warn('Validator \"' + name + '\" is already defined.');\n    else if (ParsleyDefaults.hasOwnProperty(name)) {\n      ParsleyUtils.warn('\"' + name + '\" is a restricted keyword and is not a valid validator name.');\n      return;\n    }\n    return this._setValidator(...arguments);\n  },\n\n  updateValidator: function (name, arg1, arg2) {\n    if (!this.validators[name]) {\n      ParsleyUtils.warn('Validator \"' + name + '\" is not already defined.');\n      return this.addValidator(...arguments);\n    }\n    return this._setValidator(this, arguments);\n  },\n\n  removeValidator: function (name) {\n    if (!this.validators[name])\n      ParsleyUtils.warn('Validator \"' + name + '\" is not defined.');\n\n    delete this.validators[name];\n\n    return this;\n  },\n\n  _setValidator: function (name, validator, priority) {\n    if ('object' !== typeof validator) {\n      // Old style validator, with `fn` and `priority`\n      validator = {\n        fn: validator,\n        priority: priority\n      };\n    }\n    if (!validator.validate) {\n      validator = new ParsleyValidator(validator);\n    }\n    this.validators[name] = validator;\n\n    for (var locale in validator.messages || {})\n      this.addMessage(locale, name, validator.messages[locale]);\n\n    return this;\n  },\n\n  getErrorMessage: function (constraint) {\n    var message;\n\n    // Type constraints are a bit different, we have to match their requirements too to find right error message\n    if ('type' === constraint.name) {\n      var typeMessages = this.catalog[this.locale][constraint.name] || {};\n      message = typeMessages[constraint.requirements];\n    } else\n      message = this.formatMessage(this.catalog[this.locale][constraint.name], constraint.requirements);\n\n    return message || this.catalog[this.locale].defaultMessage || this.catalog.en.defaultMessage;\n  },\n\n  // Kind of light `sprintf()` implementation\n  formatMessage: function (string, parameters) {\n    if ('object' === typeof parameters) {\n      for (var i in parameters)\n        string = this.formatMessage(string, parameters[i]);\n\n      return string;\n    }\n\n    return 'string' === typeof string ? string.replace(/%s/i, parameters) : '';\n  },\n\n  // Here is the Parsley default validators list.\n  // A validator is an object with the following key values:\n  //  - priority: an integer\n  //  - requirement: 'string' (default), 'integer', 'number', 'regexp' or an Array of these\n  //  - validateString, validateMultiple, validateNumber: functions returning `true`, `false` or a promise\n  // Alternatively, a validator can be a function that returns such an object\n  //\n  validators: {\n    notblank: {\n      validateString: function(value) {\n        return /\\S/.test(value);\n      },\n      priority: 2\n    },\n    required: {\n      validateMultiple: function(values) {\n        return values.length > 0;\n      },\n      validateString: function(value) {\n        return /\\S/.test(value);\n      },\n      priority: 512\n    },\n    type: {\n      validateString: function(value, type, {step = '1', base = 0} = {}) {\n        var regex = typeRegexes[type];\n        if (!regex) {\n          throw new Error('validator type `' + type + '` is not supported');\n        }\n        if (!regex.test(value))\n          return false;\n        if ('number' === type) {\n          if (!/^any$/i.test(step || '')) {\n            var nb = Number(value);\n            var decimals = Math.max(decimalPlaces(step), decimalPlaces(base));\n            if (decimalPlaces(nb) > decimals) // Value can't have too many decimals\n              return false;\n            // Be careful of rounding errors by using integers.\n            var toInt = f => { return Math.round(f * Math.pow(10, decimals)); };\n            if ((toInt(nb) - toInt(base)) % toInt(step) != 0)\n              return false;\n          }\n        }\n        return true;\n      },\n      requirementType: {\n        '': 'string',\n        step: 'string',\n        base: 'number'\n      },\n      priority: 256\n    },\n    pattern: {\n      validateString: function(value, regexp) {\n        return regexp.test(value);\n      },\n      requirementType: 'regexp',\n      priority: 64\n    },\n    minlength: {\n      validateString: function (value, requirement) {\n        return value.length >= requirement;\n      },\n      requirementType: 'integer',\n      priority: 30\n    },\n    maxlength: {\n      validateString: function (value, requirement) {\n        return value.length <= requirement;\n      },\n      requirementType: 'integer',\n      priority: 30\n    },\n    length: {\n      validateString: function (value, min, max) {\n        return value.length >= min && value.length <= max;\n      },\n      requirementType: ['integer', 'integer'],\n      priority: 30\n    },\n    mincheck: {\n      validateMultiple: function (values, requirement) {\n        return values.length >= requirement;\n      },\n      requirementType: 'integer',\n      priority: 30\n    },\n    maxcheck: {\n      validateMultiple: function (values, requirement) {\n        return values.length <= requirement;\n      },\n      requirementType: 'integer',\n      priority: 30\n    },\n    check: {\n      validateMultiple: function (values, min, max) {\n        return values.length >= min && values.length <= max;\n      },\n      requirementType: ['integer', 'integer'],\n      priority: 30\n    },\n    min: {\n      validateNumber: function (value, requirement) {\n        return value >= requirement;\n      },\n      requirementType: 'number',\n      priority: 30\n    },\n    max: {\n      validateNumber: function (value, requirement) {\n        return value <= requirement;\n      },\n      requirementType: 'number',\n      priority: 30\n    },\n    range: {\n      validateNumber: function (value, min, max) {\n        return value >= min && value <= max;\n      },\n      requirementType: ['number', 'number'],\n      priority: 30\n    },\n    equalto: {\n      validateString: function (value, refOrValue) {\n        var $reference = $(refOrValue);\n        if ($reference.length)\n          return value === $reference.val();\n        else\n          return value === refOrValue;\n      },\n      priority: 256\n    }\n  }\n};\n\nexport default ParsleyValidatorRegistry;\n", "import $ from 'jquery';\nimport ParsleyUtils from './utils';\n\nvar ParsleyUI = {};\n\nvar diffResults = function (newResult, oldResult, deep) {\n  var added = [];\n  var kept = [];\n\n  for (var i = 0; i < newResult.length; i++) {\n    var found = false;\n\n    for (var j = 0; j < oldResult.length; j++)\n      if (newResult[i].assert.name === oldResult[j].assert.name) {\n        found = true;\n        break;\n      }\n\n    if (found)\n      kept.push(newResult[i]);\n    else\n      added.push(newResult[i]);\n  }\n\n  return {\n    kept: kept,\n    added: added,\n    removed: !deep ? diffResults(oldResult, newResult, true).added : []\n  };\n};\n\nParsleyUI.Form = {\n\n  _actualizeTriggers: function () {\n    this.$element.on('submit.Parsley', evt => { this.onSubmitValidate(evt); });\n    this.$element.on('click.Parsley', 'input[type=\"submit\"], button[type=\"submit\"]', evt => { this.onSubmitButton(evt); });\n\n    // UI could be disabled\n    if (false === this.options.uiEnabled)\n      return;\n\n    this.$element.attr('novalidate', '');\n  },\n\n  focus: function () {\n    this._focusedField = null;\n\n    if (true === this.validationResult || 'none' === this.options.focus)\n      return null;\n\n    for (var i = 0; i < this.fields.length; i++) {\n      var field = this.fields[i];\n      if (true !== field.validationResult && field.validationResult.length > 0 && 'undefined' === typeof field.options.noFocus) {\n        this._focusedField = field.$element;\n        if ('first' === this.options.focus)\n          break;\n      }\n    }\n\n    if (null === this._focusedField)\n      return null;\n\n    return this._focusedField.focus();\n  },\n\n  _destroyUI: function () {\n    // Reset all event listeners\n    this.$element.off('.Parsley');\n  }\n\n};\n\nParsleyUI.Field = {\n\n  _reflowUI: function () {\n    this._buildUI();\n\n    // If this field doesn't have an active UI don't bother doing something\n    if (!this._ui)\n      return;\n\n    // Diff between two validation results\n    var diff = diffResults(this.validationResult, this._ui.lastValidationResult);\n\n    // Then store current validation result for next reflow\n    this._ui.lastValidationResult = this.validationResult;\n\n    // Handle valid / invalid / none field class\n    this._manageStatusClass();\n\n    // Add, remove, updated errors messages\n    this._manageErrorsMessages(diff);\n\n    // Triggers impl\n    this._actualizeTriggers();\n\n    // If field is not valid for the first time, bind keyup trigger to ease UX and quickly inform user\n    if ((diff.kept.length || diff.added.length) && !this._failedOnce) {\n      this._failedOnce = true;\n      this._actualizeTriggers();\n    }\n  },\n\n  // Returns an array of field's error message(s)\n  getErrorsMessages: function () {\n    // No error message, field is valid\n    if (true === this.validationResult)\n      return [];\n\n    var messages = [];\n\n    for (var i = 0; i < this.validationResult.length; i++)\n      messages.push(this.validationResult[i].errorMessage ||\n       this._getErrorMessage(this.validationResult[i].assert));\n\n    return messages;\n  },\n\n  // It's a goal of Parsley that this method is no longer required [#1073]\n  addError: function (name, {message, assert, updateClass = true} = {}) {\n    this._buildUI();\n    this._addError(name, {message, assert});\n\n    if (updateClass)\n      this._errorClass();\n  },\n\n  // It's a goal of Parsley that this method is no longer required [#1073]\n  updateError: function (name, {message, assert, updateClass = true} = {}) {\n    this._buildUI();\n    this._updateError(name, {message, assert});\n\n    if (updateClass)\n      this._errorClass();\n  },\n\n  // It's a goal of Parsley that this method is no longer required [#1073]\n  removeError: function (name, {updateClass = true} = {}) {\n    this._buildUI();\n    this._removeError(name);\n\n    // edge case possible here: remove a standard Parsley error that is still failing in this.validationResult\n    // but highly improbable cuz' manually removing a well Parsley handled error makes no sense.\n    if (updateClass)\n      this._manageStatusClass();\n  },\n\n  _manageStatusClass: function () {\n    if (this.hasConstraints() && this.needsValidation() && true === this.validationResult)\n      this._successClass();\n    else if (this.validationResult.length > 0)\n      this._errorClass();\n    else\n      this._resetClass();\n  },\n\n  _manageErrorsMessages: function (diff) {\n    if ('undefined' !== typeof this.options.errorsMessagesDisabled)\n      return;\n\n    // Case where we have errorMessage option that configure an unique field error message, regardless failing validators\n    if ('undefined' !== typeof this.options.errorMessage) {\n      if ((diff.added.length || diff.kept.length)) {\n        this._insertErrorWrapper();\n\n        if (0 === this._ui.$errorsWrapper.find('.parsley-custom-error-message').length)\n          this._ui.$errorsWrapper\n            .append(\n              $(this.options.errorTemplate)\n              .addClass('parsley-custom-error-message')\n            );\n\n        return this._ui.$errorsWrapper\n          .addClass('filled')\n          .find('.parsley-custom-error-message')\n          .html(this.options.errorMessage);\n      }\n\n      return this._ui.$errorsWrapper\n        .removeClass('filled')\n        .find('.parsley-custom-error-message')\n        .remove();\n    }\n\n    // Show, hide, update failing constraints messages\n    for (var i = 0; i < diff.removed.length; i++)\n      this._removeError(diff.removed[i].assert.name);\n\n    for (i = 0; i < diff.added.length; i++)\n      this._addError(diff.added[i].assert.name, {message: diff.added[i].errorMessage, assert: diff.added[i].assert});\n\n    for (i = 0; i < diff.kept.length; i++)\n      this._updateError(diff.kept[i].assert.name, {message: diff.kept[i].errorMessage, assert: diff.kept[i].assert});\n  },\n\n\n  _addError: function (name, {message, assert}) {\n    this._insertErrorWrapper();\n    this._ui.$errorsWrapper\n      .addClass('filled')\n      .append(\n        $(this.options.errorTemplate)\n        .addClass('parsley-' + name)\n        .html(message || this._getErrorMessage(assert))\n      );\n  },\n\n  _updateError: function (name, {message, assert}) {\n    this._ui.$errorsWrapper\n      .addClass('filled')\n      .find('.parsley-' + name)\n      .html(message || this._getErrorMessage(assert));\n  },\n\n  _removeError: function (name) {\n    this._ui.$errorsWrapper\n      .removeClass('filled')\n      .find('.parsley-' + name)\n      .remove();\n  },\n\n  _getErrorMessage: function (constraint) {\n    var customConstraintErrorMessage = constraint.name + 'Message';\n\n    if ('undefined' !== typeof this.options[customConstraintErrorMessage])\n      return window.Parsley.formatMessage(this.options[customConstraintErrorMessage], constraint.requirements);\n\n    return window.Parsley.getErrorMessage(constraint);\n  },\n\n  _buildUI: function () {\n    // UI could be already built or disabled\n    if (this._ui || false === this.options.uiEnabled)\n      return;\n\n    var _ui = {};\n\n    // Give field its Parsley id in DOM\n    this.$element.attr(this.options.namespace + 'id', this.__id__);\n\n    /** Generate important UI elements and store them in this **/\n    // $errorClassHandler is the $element that woul have parsley-error and parsley-success classes\n    _ui.$errorClassHandler = this._manageClassHandler();\n\n    // $errorsWrapper is a div that would contain the various field errors, it will be appended into $errorsContainer\n    _ui.errorsWrapperId = 'parsley-id-' + (this.options.multiple ? 'multiple-' + this.options.multiple : this.__id__);\n    _ui.$errorsWrapper = $(this.options.errorsWrapper).attr('id', _ui.errorsWrapperId);\n\n    // ValidationResult UI storage to detect what have changed bwt two validations, and update DOM accordingly\n    _ui.lastValidationResult = [];\n    _ui.validationInformationVisible = false;\n\n    // Store it in this for later\n    this._ui = _ui;\n  },\n\n  // Determine which element will have `parsley-error` and `parsley-success` classes\n  _manageClassHandler: function () {\n    // An element selector could be passed through DOM with `data-parsley-class-handler=#foo`\n    if ('string' === typeof this.options.classHandler && $(this.options.classHandler).length)\n      return $(this.options.classHandler);\n\n    // Class handled could also be determined by function given in Parsley options\n    var $handler = this.options.classHandler.call(this, this);\n\n    // If this function returned a valid existing DOM element, go for it\n    if ('undefined' !== typeof $handler && $handler.length)\n      return $handler;\n\n    // Otherwise, if simple element (input, texatrea, select...) it will perfectly host the classes\n    if (!this.options.multiple || this.$element.is('select'))\n      return this.$element;\n\n    // But if multiple element (radio, checkbox), that would be their parent\n    return this.$element.parent();\n  },\n\n  _insertErrorWrapper: function () {\n    var $errorsContainer;\n\n    // Nothing to do if already inserted\n    if (0 !== this._ui.$errorsWrapper.parent().length)\n      return this._ui.$errorsWrapper.parent();\n\n    if ('string' === typeof this.options.errorsContainer) {\n      if ($(this.options.errorsContainer).length)\n        return $(this.options.errorsContainer).append(this._ui.$errorsWrapper);\n      else\n        ParsleyUtils.warn('The errors container `' + this.options.errorsContainer + '` does not exist in DOM');\n    } else if ('function' === typeof this.options.errorsContainer)\n      $errorsContainer = this.options.errorsContainer.call(this, this);\n\n    if ('undefined' !== typeof $errorsContainer && $errorsContainer.length)\n      return $errorsContainer.append(this._ui.$errorsWrapper);\n\n    var $from = this.$element;\n    if (this.options.multiple)\n      $from = $from.parent();\n    return $from.after(this._ui.$errorsWrapper);\n  },\n\n  _actualizeTriggers: function () {\n    var $toBind = this._findRelated();\n\n    // Remove Parsley events already bound on this field\n    $toBind.off('.Parsley');\n    if (this._failedOnce)\n      $toBind.on(ParsleyUtils.namespaceEvents(this.options.triggerAfterFailure, 'Parsley'), () => {\n        this.validate();\n      });\n    else {\n      $toBind.on(ParsleyUtils.namespaceEvents(this.options.trigger, 'Parsley'), event => {\n        this._eventValidate(event);\n      });\n    }\n  },\n\n  _eventValidate: function (event) {\n    // For keyup, keypress, keydown, input... events that could be a little bit obstrusive\n    // do not validate if val length < min threshold on first validation. Once field have been validated once and info\n    // about success or failure have been displayed, always validate with this trigger to reflect every yalidation change.\n    if (/key|input/.test(event.type))\n      if (!(this._ui && this._ui.validationInformationVisible) && this.getValue().length <= this.options.validationThreshold)\n        return;\n\n    this.validate();\n  },\n\n  _resetUI: function () {\n    // Reset all event listeners\n    this._failedOnce = false;\n    this._actualizeTriggers();\n\n    // Nothing to do if UI never initialized for this field\n    if ('undefined' === typeof this._ui)\n      return;\n\n    // Reset all errors' li\n    this._ui.$errorsWrapper\n      .removeClass('filled')\n      .children()\n      .remove();\n\n    // Reset validation class\n    this._resetClass();\n\n    // Reset validation flags and last validation result\n    this._ui.lastValidationResult = [];\n    this._ui.validationInformationVisible = false;\n  },\n\n  _destroyUI: function () {\n    this._resetUI();\n\n    if ('undefined' !== typeof this._ui)\n      this._ui.$errorsWrapper.remove();\n\n    delete this._ui;\n  },\n\n  _successClass: function () {\n    this._ui.validationInformationVisible = true;\n    this._ui.$errorClassHandler.removeClass(this.options.errorClass).addClass(this.options.successClass);\n  },\n  _errorClass: function () {\n    this._ui.validationInformationVisible = true;\n    this._ui.$errorClassHandler.removeClass(this.options.successClass).addClass(this.options.errorClass);\n  },\n  _resetClass: function () {\n    this._ui.$errorClassHandler.removeClass(this.options.successClass).removeClass(this.options.errorClass);\n  }\n};\n\nexport default ParsleyUI;\n", "import $ from 'jquery';\nimport Pa<PERSON>leyAbstract from './abstract';\nimport ParsleyUtils from './utils';\n\nvar ParsleyForm = function (element, domOptions, options) {\n  this.__class__ = 'ParsleyForm';\n  this.__id__ = ParsleyUtils.generateID();\n\n  this.$element = $(element);\n  this.domOptions = domOptions;\n  this.options = options;\n  this.parent = window.Parsley;\n\n  this.fields = [];\n  this.validationResult = null;\n};\n\nvar statusMapping = {pending: null, resolved: true, rejected: false};\n\nParsleyForm.prototype = {\n  onSubmitValidate: function (event) {\n    // This is a Parsley generated submit event, do not validate, do not prevent, simply exit and keep normal behavior\n    if (true === event.parsley)\n      return;\n\n    // If we didn't come here through a submit button, use the first one in the form\n    var $submitSource = this._$submitSource || this.$element.find('input[type=\"submit\"], button[type=\"submit\"]').first();\n    this._$submitSource = null;\n    this.$element.find('.parsley-synthetic-submit-button').prop('disabled', true);\n    if ($submitSource.is('[formnovalidate]'))\n      return;\n\n    var promise = this.whenValidate({event});\n\n    if ('resolved' === promise.state() && false !== this._trigger('submit')) {\n      // All good, let event go through. We make this distinction because browsers\n      // differ in their handling of `submit` being called from inside a submit event [#1047]\n    } else {\n      // Rejected or pending: cancel this submit\n      event.stopImmediatePropagation();\n      event.preventDefault();\n      if ('pending' === promise.state())\n        promise.done(() => { this._submit($submitSource); });\n    }\n  },\n\n  onSubmitButton: function(event) {\n    this._$submitSource = $(event.target);\n  },\n  // internal\n  // _submit submits the form, this time without going through the validations.\n  // Care must be taken to \"fake\" the actual submit button being clicked.\n  _submit: function ($submitSource) {\n    if (false === this._trigger('submit'))\n      return;\n    // Add submit button's data\n    if ($submitSource) {\n      var $synthetic = this.$element.find('.parsley-synthetic-submit-button').prop('disabled', false);\n      if (0 === $synthetic.length)\n        $synthetic = $('<input class=\"parsley-synthetic-submit-button\" type=\"hidden\">').appendTo(this.$element);\n      $synthetic.attr({\n        name: $submitSource.attr('name'),\n        value: $submitSource.attr('value')\n      });\n    }\n\n    this.$element.trigger($.extend($.Event('submit'), {parsley: true}));\n  },\n\n  // Performs validation on fields while triggering events.\n  // @returns `true` if all validations succeeds, `false`\n  // if a failure is immediately detected, or `null`\n  // if dependant on a promise.\n  // Consider using `whenValidate` instead.\n  validate: function (options) {\n    if (arguments.length >= 1 && !$.isPlainObject(options)) {\n      ParsleyUtils.warnOnce('Calling validate on a parsley form without passing arguments as an object is deprecated.');\n      var [group, force, event] = arguments;\n      options = {group, force, event};\n    }\n    return statusMapping[ this.whenValidate(options).state() ];\n  },\n\n  whenValidate: function ({group, force, event} = {}) {\n    this.submitEvent = event;\n    if (event) {\n      this.submitEvent = $.extend({}, event, {preventDefault: () => {\n        ParsleyUtils.warnOnce(\"Using `this.submitEvent.preventDefault()` is deprecated; instead, call `this.validationResult = false`\");\n        this.validationResult = false;\n      }});\n    }\n    this.validationResult = true;\n\n    // fire validate event to eventually modify things before very validation\n    this._trigger('validate');\n\n    // Refresh form DOM options and form's fields that could have changed\n    this._refreshFields();\n\n    var promises = this._withoutReactualizingFormOptions(() => {\n      return $.map(this.fields, field => {\n        return field.whenValidate({force, group});\n      });\n    });\n\n    var promiseBasedOnValidationResult = () => {\n      var r = $.Deferred();\n      if (false === this.validationResult)\n        r.reject();\n      return r.resolve().promise();\n    };\n\n    return $.when(...promises)\n      .done(  () => { this._trigger('success'); })\n      .fail(  () => {\n        this.validationResult = false;\n        this.focus();\n        this._trigger('error');\n      })\n      .always(() => { this._trigger('validated'); })\n      .pipe(  promiseBasedOnValidationResult, promiseBasedOnValidationResult);\n  },\n\n  // Iterate over refreshed fields, and stop on first failure.\n  // Returns `true` if all fields are valid, `false` if a failure is detected\n  // or `null` if the result depends on an unresolved promise.\n  // Prefer using `whenValid` instead.\n  isValid: function (options) {\n    if (arguments.length >= 1 && !$.isPlainObject(options)) {\n      ParsleyUtils.warnOnce('Calling isValid on a parsley form without passing arguments as an object is deprecated.');\n      var [group, force] = arguments;\n      options = {group, force};\n    }\n    return statusMapping[ this.whenValid(options).state() ];\n  },\n\n  // Iterate over refreshed fields and validate them.\n  // Returns a promise.\n  // A validation that immediately fails will interrupt the validations.\n  whenValid: function ({group, force} = {}) {\n    this._refreshFields();\n\n    var promises = this._withoutReactualizingFormOptions(() => {\n      return $.map(this.fields, field => {\n        return field.whenValid({group, force});\n      });\n    });\n    return $.when(...promises);\n  },\n\n  _refreshFields: function () {\n    return this.actualizeOptions()._bindFields();\n  },\n\n  _bindFields: function () {\n    var oldFields = this.fields;\n\n    this.fields = [];\n    this.fieldsMappedById = {};\n\n    this._withoutReactualizingFormOptions(() => {\n      this.$element\n      .find(this.options.inputs)\n      .not(this.options.excluded)\n      .each((_, element) => {\n        var fieldInstance = new window.Parsley.Factory(element, {}, this);\n\n        // Only add valid and not excluded `ParsleyField` and `ParsleyFieldMultiple` children\n        if (('ParsleyField' === fieldInstance.__class__ || 'ParsleyFieldMultiple' === fieldInstance.__class__) && (true !== fieldInstance.options.excluded))\n          if ('undefined' === typeof this.fieldsMappedById[fieldInstance.__class__ + '-' + fieldInstance.__id__]) {\n            this.fieldsMappedById[fieldInstance.__class__ + '-' + fieldInstance.__id__] = fieldInstance;\n            this.fields.push(fieldInstance);\n          }\n      });\n\n      $(oldFields).not(this.fields).each((_, field) => {\n        field._trigger('reset');\n      });\n    });\n    return this;\n  },\n\n  // Internal only.\n  // Looping on a form's fields to do validation or similar\n  // will trigger reactualizing options on all of them, which\n  // in turn will reactualize the form's options.\n  // To avoid calling actualizeOptions so many times on the form\n  // for nothing, _withoutReactualizingFormOptions temporarily disables\n  // the method actualizeOptions on this form while `fn` is called.\n  _withoutReactualizingFormOptions: function (fn) {\n    var oldActualizeOptions = this.actualizeOptions;\n    this.actualizeOptions = function () { return this; };\n    var result = fn();\n    this.actualizeOptions = oldActualizeOptions;\n    return result;\n  },\n\n  // Internal only.\n  // Shortcut to trigger an event\n  // Returns true iff event is not interrupted and default not prevented.\n  _trigger: function (eventName) {\n    return this.trigger('form:' + eventName);\n  }\n\n};\n\nexport default ParsleyForm;\n", "import $ from 'jquery';\nimport ParsleyUtils from '../utils';\nimport ParsleyValidator from '../validator';\n\n\nvar ConstraintFactory = function (parsleyField, name, requirements, priority, isDomConstraint) {\n  if (!/ParsleyField/.test(parsleyField.__class__))\n    throw new Error('Parsley<PERSON>ield or ParsleyFieldMultiple instance expected');\n\n  var validatorSpec = window.Parsley._validatorRegistry.validators[name];\n  var validator = new ParsleyValidator(validatorSpec);\n\n  $.extend(this, {\n    validator: validator,\n    name: name,\n    requirements: requirements,\n    priority: priority || parsleyField.options[name + 'Priority'] || validator.priority,\n    isDomConstraint: true === isDomConstraint\n  });\n  this._parseRequirements(parsleyField.options);\n};\n\nvar capitalize = function(str) {\n  var cap = str[0].toUpperCase();\n  return cap + str.slice(1);\n};\n\nConstraintFactory.prototype = {\n  validate: function(value, instance) {\n    var args = this.requirementList.slice(0); // Make copy\n    args.unshift(value);\n    args.push(instance);\n    return this.validator.validate.apply(this.validator, args);\n  },\n\n  _parseRequirements: function(options) {\n    this.requirementList = this.validator.parseRequirements(this.requirements, key => {\n      return options[this.name + capitalize(key)];\n    });\n  }\n};\n\nexport default ConstraintFactory;\n\n", "import $ from 'jquery';\nimport ConstraintFactory from './factory/constraint';\nimport ParsleyUI from './ui';\nimport ParsleyUtils from './utils';\n\nvar ParsleyField = function (field, domOptions, options, parsleyFormInstance) {\n  this.__class__ = 'ParsleyField';\n  this.__id__ = ParsleyUtils.generateID();\n\n  this.$element = $(field);\n\n  // Set parent if we have one\n  if ('undefined' !== typeof parsleyFormInstance) {\n    this.parent = parsleyFormInstance;\n  }\n\n  this.options = options;\n  this.domOptions = domOptions;\n\n  // Initialize some properties\n  this.constraints = [];\n  this.constraintsByName = {};\n  this.validationResult = [];\n\n  // Bind constraints\n  this._bindConstraints();\n};\n\nvar statusMapping = {pending: null, resolved: true, rejected: false};\n\nParsleyField.prototype = {\n  // # Public API\n  // Validate field and trigger some events for mainly `ParsleyUI`\n  // @returns `true`, an array of the validators that failed, or\n  // `null` if validation is not finished. Prefer using whenValidate\n  validate: function (options) {\n    if (arguments.length >= 1 && !$.isPlainObject(options)) {\n      ParsleyUtils.warnOnce('Calling validate on a parsley field without passing arguments as an object is deprecated.');\n      options = {options};\n    }\n    var promise = this.whenValidate(options);\n    if (!promise)  // If excluded with `group` option\n      return true;\n    switch (promise.state()) {\n      case 'pending': return null;\n      case 'resolved': return true;\n      case 'rejected': return this.validationResult;\n    }\n  },\n\n  // Validate field and trigger some events for mainly `ParsleyUI`\n  // @returns a promise that succeeds only when all validations do\n  // or `undefined` if field is not in the given `group`.\n  whenValidate: function ({force, group} =  {}) {\n    // do not validate a field if not the same as given validation group\n    this.refreshConstraints();\n    if (group && !this._isInGroup(group))\n      return;\n\n    this.value = this.getValue();\n\n    // Field Validate event. `this.value` could be altered for custom needs\n    this._trigger('validate');\n\n    return this.whenValid({force, value: this.value, _refreshed: true})\n      .always(() => { this._reflowUI(); })\n      .done(() =>   { this._trigger('success'); })\n      .fail(() =>   { this._trigger('error'); })\n      .always(() => { this._trigger('validated'); });\n  },\n\n  hasConstraints: function () {\n    return 0 !== this.constraints.length;\n  },\n\n  // An empty optional field does not need validation\n  needsValidation: function (value) {\n    if ('undefined' === typeof value)\n      value = this.getValue();\n\n    // If a field is empty and not required, it is valid\n    // Except if `data-parsley-validate-if-empty` explicitely added, useful for some custom validators\n    if (!value.length && !this._isRequired() && 'undefined' === typeof this.options.validateIfEmpty)\n      return false;\n\n    return true;\n  },\n\n  _isInGroup: function (group) {\n    if ($.isArray(this.options.group))\n      return -1 !== $.inArray(group, this.options.group);\n    return this.options.group === group;\n  },\n\n  // Just validate field. Do not trigger any event.\n  // Returns `true` iff all constraints pass, `false` if there are failures,\n  // or `null` if the result can not be determined yet (depends on a promise)\n  // See also `whenValid`.\n  isValid: function (options) {\n    if (arguments.length >= 1 && !$.isPlainObject(options)) {\n      ParsleyUtils.warnOnce('Calling isValid on a parsley field without passing arguments as an object is deprecated.');\n      var [force, value] = arguments;\n      options = {force, value};\n    }\n    var promise = this.whenValid(options);\n    if (!promise) // Excluded via `group`\n      return true;\n    return statusMapping[promise.state()];\n  },\n\n  // Just validate field. Do not trigger any event.\n  // @returns a promise that succeeds only when all validations do\n  // or `undefined` if the field is not in the given `group`.\n  // The argument `force` will force validation of empty fields.\n  // If a `value` is given, it will be validated instead of the value of the input.\n  whenValid: function ({force = false, value, group, _refreshed} = {}) {\n    // Recompute options and rebind constraints to have latest changes\n    if (!_refreshed)\n      this.refreshConstraints();\n    // do not validate a field if not the same as given validation group\n    if (group && !this._isInGroup(group))\n      return;\n\n    this.validationResult = true;\n\n    // A field without constraint is valid\n    if (!this.hasConstraints())\n      return $.when();\n\n    // Value could be passed as argument, needed to add more power to 'parsley:field:validate'\n    if ('undefined' === typeof value || null === value)\n      value = this.getValue();\n\n    if (!this.needsValidation(value) && true !== force)\n      return $.when();\n\n    var groupedConstraints = this._getGroupedConstraints();\n    var promises = [];\n    $.each(groupedConstraints, (_, constraints) => {\n      // Process one group of constraints at a time, we validate the constraints\n      // and combine the promises together.\n      var promise = $.when(\n        ...$.map(constraints, constraint => this._validateConstraint(value, constraint))\n      );\n      promises.push(promise);\n      if (promise.state() === 'rejected')\n        return false; // Interrupt processing if a group has already failed\n    });\n    return $.when.apply($, promises);\n  },\n\n  // @returns a promise\n  _validateConstraint: function(value, constraint) {\n    var result = constraint.validate(value, this);\n    // Map false to a failed promise\n    if (false === result)\n      result = $.Deferred().reject();\n    // Make sure we return a promise and that we record failures\n    return $.when(result).fail(errorMessage => {\n      if (true === this.validationResult)\n        this.validationResult = [];\n      this.validationResult.push({\n        assert: constraint,\n        errorMessage: 'string' === typeof errorMessage && errorMessage\n      });\n    });\n  },\n\n  // @returns Parsley field computed value that could be overrided or configured in DOM\n  getValue: function () {\n    var value;\n\n    // Value could be overriden in DOM or with explicit options\n    if ('function' === typeof this.options.value)\n      value = this.options.value(this);\n    else if ('undefined' !== typeof this.options.value)\n      value = this.options.value;\n    else\n      value = this.$element.val();\n\n    // Handle wrong DOM or configurations\n    if ('undefined' === typeof value || null === value)\n      return '';\n\n    return this._handleWhitespace(value);\n  },\n\n  // Actualize options that could have change since previous validation\n  // Re-bind accordingly constraints (could be some new, removed or updated)\n  refreshConstraints: function () {\n    return this.actualizeOptions()._bindConstraints();\n  },\n\n  /**\n  * Add a new constraint to a field\n  *\n  * @param {String}   name\n  * @param {Mixed}    requirements      optional\n  * @param {Number}   priority          optional\n  * @param {Boolean}  isDomConstraint   optional\n  */\n  addConstraint: function (name, requirements, priority, isDomConstraint) {\n\n    if (window.Parsley._validatorRegistry.validators[name]) {\n      var constraint = new ConstraintFactory(this, name, requirements, priority, isDomConstraint);\n\n      // if constraint already exist, delete it and push new version\n      if ('undefined' !== this.constraintsByName[constraint.name])\n        this.removeConstraint(constraint.name);\n\n      this.constraints.push(constraint);\n      this.constraintsByName[constraint.name] = constraint;\n    }\n\n    return this;\n  },\n\n  // Remove a constraint\n  removeConstraint: function (name) {\n    for (var i = 0; i < this.constraints.length; i++)\n      if (name === this.constraints[i].name) {\n        this.constraints.splice(i, 1);\n        break;\n      }\n    delete this.constraintsByName[name];\n    return this;\n  },\n\n  // Update a constraint (Remove + re-add)\n  updateConstraint: function (name, parameters, priority) {\n    return this.removeConstraint(name)\n      .addConstraint(name, parameters, priority);\n  },\n\n  // # Internals\n\n  // Internal only.\n  // Bind constraints from config + options + DOM\n  _bindConstraints: function () {\n    var constraints = [];\n    var constraintsByName = {};\n\n    // clean all existing DOM constraints to only keep javascript user constraints\n    for (var i = 0; i < this.constraints.length; i++)\n      if (false === this.constraints[i].isDomConstraint) {\n        constraints.push(this.constraints[i]);\n        constraintsByName[this.constraints[i].name] = this.constraints[i];\n      }\n\n    this.constraints = constraints;\n    this.constraintsByName = constraintsByName;\n\n    // then re-add Parsley DOM-API constraints\n    for (var name in this.options)\n      this.addConstraint(name, this.options[name], undefined, true);\n\n    // finally, bind special HTML5 constraints\n    return this._bindHtml5Constraints();\n  },\n\n  // Internal only.\n  // Bind specific HTML5 constraints to be HTML5 compliant\n  _bindHtml5Constraints: function () {\n    // html5 required\n    if (this.$element.hasClass('required') || this.$element.attr('required'))\n      this.addConstraint('required', true, undefined, true);\n\n    // html5 pattern\n    if ('string' === typeof this.$element.attr('pattern'))\n      this.addConstraint('pattern', this.$element.attr('pattern'), undefined, true);\n\n    // range\n    if ('undefined' !== typeof this.$element.attr('min') && 'undefined' !== typeof this.$element.attr('max'))\n      this.addConstraint('range', [this.$element.attr('min'), this.$element.attr('max')], undefined, true);\n\n    // HTML5 min\n    else if ('undefined' !== typeof this.$element.attr('min'))\n      this.addConstraint('min', this.$element.attr('min'), undefined, true);\n\n    // HTML5 max\n    else if ('undefined' !== typeof this.$element.attr('max'))\n      this.addConstraint('max', this.$element.attr('max'), undefined, true);\n\n\n    // length\n    if ('undefined' !== typeof this.$element.attr('minlength') && 'undefined' !== typeof this.$element.attr('maxlength'))\n      this.addConstraint('length', [this.$element.attr('minlength'), this.$element.attr('maxlength')], undefined, true);\n\n    // HTML5 minlength\n    else if ('undefined' !== typeof this.$element.attr('minlength'))\n      this.addConstraint('minlength', this.$element.attr('minlength'), undefined, true);\n\n    // HTML5 maxlength\n    else if ('undefined' !== typeof this.$element.attr('maxlength'))\n      this.addConstraint('maxlength', this.$element.attr('maxlength'), undefined, true);\n\n\n    // html5 types\n    var type = this.$element.attr('type');\n\n    if ('undefined' === typeof type)\n      return this;\n\n    // Small special case here for HTML5 number: integer validator if step attribute is undefined or an integer value, number otherwise\n    if ('number' === type) {\n      return this.addConstraint('type', ['number', {\n        step: this.$element.attr('step'),\n        base: this.$element.attr('min') || this.$element.attr('value')\n      }], undefined, true);\n    // Regular other HTML5 supported types\n    } else if (/^(email|url|range)$/i.test(type)) {\n      return this.addConstraint('type', type, undefined, true);\n    }\n    return this;\n  },\n\n  // Internal only.\n  // Field is required if have required constraint without `false` value\n  _isRequired: function () {\n    if ('undefined' === typeof this.constraintsByName.required)\n      return false;\n\n    return false !== this.constraintsByName.required.requirements;\n  },\n\n  // Internal only.\n  // Shortcut to trigger an event\n  _trigger: function (eventName) {\n    return this.trigger('field:' + eventName);\n  },\n\n  // Internal only\n  // Handles whitespace in a value\n  // Use `data-parsley-whitespace=\"squish\"` to auto squish input value\n  // Use `data-parsley-whitespace=\"trim\"` to auto trim input value\n  _handleWhitespace: function (value) {\n    if (true === this.options.trimValue)\n      ParsleyUtils.warnOnce('data-parsley-trim-value=\"true\" is deprecated, please use data-parsley-whitespace=\"trim\"');\n\n    if ('squish' === this.options.whitespace)\n      value = value.replace(/\\s{2,}/g, ' ');\n\n    if (('trim' === this.options.whitespace) || ('squish' === this.options.whitespace) || (true === this.options.trimValue))\n      value = ParsleyUtils.trimString(value);\n\n    return value;\n  },\n\n  // Internal only.\n  // Returns the constraints, grouped by descending priority.\n  // The result is thus an array of arrays of constraints.\n  _getGroupedConstraints: function () {\n    if (false === this.options.priorityEnabled)\n      return [this.constraints];\n\n    var groupedConstraints = [];\n    var index = {};\n\n    // Create array unique of priorities\n    for (var i = 0; i < this.constraints.length; i++) {\n      var p = this.constraints[i].priority;\n      if (!index[p])\n        groupedConstraints.push(index[p] = []);\n      index[p].push(this.constraints[i]);\n    }\n    // Sort them by priority DESC\n    groupedConstraints.sort(function (a, b) { return b[0].priority - a[0].priority; });\n\n    return groupedConstraints;\n  }\n\n};\n\nexport default ParsleyField;\n", "import $ from 'jquery';\n\nvar ParsleyMultiple = function () {\n  this.__class__ = 'ParsleyFieldMultiple';\n};\n\nParsleyMultiple.prototype = {\n  // Add new `$element` sibling for multiple field\n  addElement: function ($element) {\n    this.$elements.push($element);\n\n    return this;\n  },\n\n  // See `ParsleyField.refreshConstraints()`\n  refreshConstraints: function () {\n    var fieldConstraints;\n\n    this.constraints = [];\n\n    // Select multiple special treatment\n    if (this.$element.is('select')) {\n      this.actualizeOptions()._bindConstraints();\n\n      return this;\n    }\n\n    // Gather all constraints for each input in the multiple group\n    for (var i = 0; i < this.$elements.length; i++) {\n\n      // Check if element have not been dynamically removed since last binding\n      if (!$('html').has(this.$elements[i]).length) {\n        this.$elements.splice(i, 1);\n        continue;\n      }\n\n      fieldConstraints = this.$elements[i].data('ParsleyFieldMultiple').refreshConstraints().constraints;\n\n      for (var j = 0; j < fieldConstraints.length; j++)\n        this.addConstraint(fieldConstraints[j].name, fieldConstraints[j].requirements, fieldConstraints[j].priority, fieldConstraints[j].isDomConstraint);\n    }\n\n    return this;\n  },\n\n  // See `ParsleyField.getValue()`\n  getValue: function () {\n    // Value could be overriden in DOM\n    if ('function' === typeof this.options.value)\n      value = this.options.value(this);\n    else if ('undefined' !== typeof this.options.value)\n      return this.options.value;\n\n    // Radio input case\n    if (this.$element.is('input[type=radio]'))\n      return this._findRelated().filter(':checked').val() || '';\n\n    // checkbox input case\n    if (this.$element.is('input[type=checkbox]')) {\n      var values = [];\n\n      this._findRelated().filter(':checked').each(function () {\n        values.push($(this).val());\n      });\n\n      return values;\n    }\n\n    // Select multiple case\n    if (this.$element.is('select') && null === this.$element.val())\n      return [];\n\n    // Default case that should never happen\n    return this.$element.val();\n  },\n\n  _init: function () {\n    this.$elements = [this.$element];\n\n    return this;\n  }\n};\n\nexport default ParsleyMultiple;\n", "import $ from 'jquery';\nimport ParsleyUtils from './utils';\nimport ParsleyAbstract from './abstract';\nimport ParsleyForm from './form';\nimport ParsleyField from './field';\nimport ParsleyMultiple from './multiple';\n\nvar ParsleyFactory = function (element, options, parsleyFormInstance) {\n  this.$element = $(element);\n\n  // If the element has already been bound, returns its saved Parsley instance\n  var savedparsleyFormInstance = this.$element.data('Parsley');\n  if (savedparsleyFormInstance) {\n\n    // If the saved instance has been bound without a ParsleyForm parent and there is one given in this call, add it\n    if ('undefined' !== typeof parsleyFormInstance && savedparsleyFormInstance.parent === window.Parsley) {\n      savedparsleyFormInstance.parent = parsleyFormInstance;\n      savedparsleyFormInstance._resetOptions(savedparsleyFormInstance.options);\n    }\n\n    return savedparsleyFormInstance;\n  }\n\n  // Pa<PERSON><PERSON> must be instantiated with a DOM element or jQuery $element\n  if (!this.$element.length)\n    throw new Error('You must bind <PERSON><PERSON><PERSON> on an existing element.');\n\n  if ('undefined' !== typeof parsleyFormInstance && 'ParsleyForm' !== parsleyFormInstance.__class__)\n    throw new Error('Parent instance must be a ParsleyForm instance');\n\n  this.parent = parsleyFormInstance || window.Parsley;\n  return this.init(options);\n};\n\nParsleyFactory.prototype = {\n  init: function (options) {\n    this.__class__ = 'Parsley';\n    this.__version__ = '@@version';\n    this.__id__ = ParsleyUtils.generateID();\n\n    // Pre-compute options\n    this._resetOptions(options);\n\n    // A ParsleyForm instance is obviously a `<form>` element but also every node that is not an input and has the `data-parsley-validate` attribute\n    if (this.$element.is('form') || (ParsleyUtils.checkAttr(this.$element, this.options.namespace, 'validate') && !this.$element.is(this.options.inputs)))\n      return this.bind('parsleyForm');\n\n    // Every other element is bound as a `ParsleyField` or `ParsleyFieldMultiple`\n    return this.isMultiple() ? this.handleMultiple() : this.bind('parsleyField');\n  },\n\n  isMultiple: function () {\n    return (this.$element.is('input[type=radio], input[type=checkbox]')) || (this.$element.is('select') && 'undefined' !== typeof this.$element.attr('multiple'));\n  },\n\n  // Multiples fields are a real nightmare :(\n  // Maybe some refactoring would be appreciated here...\n  handleMultiple: function () {\n    var name;\n    var multiple;\n    var parsleyMultipleInstance;\n\n    // Handle multiple name\n    if (this.options.multiple)\n      ; // We already have our 'multiple' identifier\n    else if ('undefined' !== typeof this.$element.attr('name') && this.$element.attr('name').length)\n      this.options.multiple = name = this.$element.attr('name');\n    else if ('undefined' !== typeof this.$element.attr('id') && this.$element.attr('id').length)\n      this.options.multiple = this.$element.attr('id');\n\n    // Special select multiple input\n    if (this.$element.is('select') && 'undefined' !== typeof this.$element.attr('multiple')) {\n      this.options.multiple = this.options.multiple || this.__id__;\n      return this.bind('parsleyFieldMultiple');\n\n    // Else for radio / checkboxes, we need a `name` or `data-parsley-multiple` to properly bind it\n    } else if (!this.options.multiple) {\n      ParsleyUtils.warn('To be bound by Parsley, a radio, a checkbox and a multiple select input must have either a name or a multiple option.', this.$element);\n      return this;\n    }\n\n    // Remove special chars\n    this.options.multiple = this.options.multiple.replace(/(:|\\.|\\[|\\]|\\{|\\}|\\$)/g, '');\n\n    // Add proper `data-parsley-multiple` to siblings if we have a valid multiple name\n    if ('undefined' !== typeof name) {\n      $('input[name=\"' + name + '\"]').each((i, input) => {\n        if ($(input).is('input[type=radio], input[type=checkbox]'))\n          $(input).attr(this.options.namespace + 'multiple', this.options.multiple);\n      });\n    }\n\n    // Check here if we don't already have a related multiple instance saved\n    var $previouslyRelated = this._findRelated();\n    for (var i = 0; i < $previouslyRelated.length; i++) {\n      parsleyMultipleInstance = $($previouslyRelated.get(i)).data('Parsley');\n      if ('undefined' !== typeof parsleyMultipleInstance) {\n\n        if (!this.$element.data('ParsleyFieldMultiple')) {\n          parsleyMultipleInstance.addElement(this.$element);\n        }\n\n        break;\n      }\n    }\n\n    // Create a secret ParsleyField instance for every multiple field. It will be stored in `data('ParsleyFieldMultiple')`\n    // And will be useful later to access classic `ParsleyField` stuff while being in a `ParsleyFieldMultiple` instance\n    this.bind('parsleyField', true);\n\n    return parsleyMultipleInstance || this.bind('parsleyFieldMultiple');\n  },\n\n  // Return proper `ParsleyForm`, `ParsleyField` or `ParsleyFieldMultiple`\n  bind: function (type, doNotStore) {\n    var parsleyInstance;\n\n    switch (type) {\n      case 'parsleyForm':\n        parsleyInstance = $.extend(\n          new ParsleyForm(this.$element, this.domOptions, this.options),\n          window.ParsleyExtend\n        )._bindFields();\n        break;\n      case 'parsleyField':\n        parsleyInstance = $.extend(\n          new ParsleyField(this.$element, this.domOptions, this.options, this.parent),\n          window.ParsleyExtend\n        );\n        break;\n      case 'parsleyFieldMultiple':\n        parsleyInstance = $.extend(\n          new ParsleyField(this.$element, this.domOptions, this.options, this.parent),\n          new ParsleyMultiple(),\n          window.ParsleyExtend\n        )._init();\n        break;\n      default:\n        throw new Error(type + 'is not a supported Parsley type');\n    }\n\n    if (this.options.multiple)\n      ParsleyUtils.setAttr(this.$element, this.options.namespace, 'multiple', this.options.multiple);\n\n    if ('undefined' !== typeof doNotStore) {\n      this.$element.data('ParsleyFieldMultiple', parsleyInstance);\n\n      return parsleyInstance;\n    }\n\n    // Store the freshly bound instance in a DOM element for later access using jQuery `data()`\n    this.$element.data('Parsley', parsleyInstance);\n\n    // Tell the world we have a new ParsleyForm or ParsleyField instance!\n    parsleyInstance._actualizeTriggers();\n    parsleyInstance._trigger('init');\n\n    return parsleyInstance;\n  }\n};\n\nexport default ParsleyFactory;\n", "import $ from 'jquery';\nimport ParsleyUtils from './utils';\nimport ParsleyDefaults from './defaults';\nimport ParsleyAbstract from './abstract';\nimport ParsleyValidatorRegistry from './validator_registry';\nimport ParsleyUI from './ui';\nimport ParsleyForm from './form';\nimport ParsleyField from './field';\nimport ParsleyMultiple from './multiple';\nimport ParsleyFactory from './factory';\n\nvar vernums = $.fn.jquery.split('.');\nif (parseInt(vernums[0]) <= 1 && parseInt(vernums[1]) < 8) {\n  throw \"The loaded version of jQuery is too old. Please upgrade to 1.8.x or better.\";\n}\nif (!vernums.forEach) {\n  ParsleyUtils.warn('Parsley requires ES5 to run properly. Please include https://github.com/es-shims/es5-shim');\n}\n// Inherit `on`, `off` & `trigger` to Parsley:\nvar Parsley = $.extend(new ParsleyAbstract(), {\n    $element: $(document),\n    actualizeOptions: null,\n    _resetOptions: null,\n    Factory: ParsleyFactory,\n    version: '@@version'\n  });\n\n// Supplement ParsleyField and Form with ParsleyAbstract\n// This way, the constructors will have access to those methods\n$.extend(ParsleyField.prototype, ParsleyUI.Field, ParsleyAbstract.prototype);\n$.extend(ParsleyForm.prototype, ParsleyUI.Form, ParsleyAbstract.prototype);\n// Inherit actualizeOptions and _resetOptions:\n$.extend(ParsleyFactory.prototype, ParsleyAbstract.prototype);\n\n// ### jQuery API\n// `$('.elem').parsley(options)` or `$('.elem').psly(options)`\n$.fn.parsley = $.fn.psly = function (options) {\n  if (this.length > 1) {\n    var instances = [];\n\n    this.each(function () {\n      instances.push($(this).parsley(options));\n    });\n\n    return instances;\n  }\n\n  // Return undefined if applied to non existing DOM element\n  if (!$(this).length) {\n    ParsleyUtils.warn('You must bind Parsley on an existing element.');\n\n    return;\n  }\n\n  return new ParsleyFactory(this, options);\n};\n\n// ### ParsleyField and ParsleyForm extension\n// Ensure the extension is now defined if it wasn't previously\nif ('undefined' === typeof window.ParsleyExtend)\n  window.ParsleyExtend = {};\n\n// ### Parsley config\n// Inherit from ParsleyDefault, and copy over any existing values\nParsley.options = $.extend(ParsleyUtils.objectCreate(ParsleyDefaults), window.ParsleyConfig);\nwindow.ParsleyConfig = Parsley.options; // Old way of accessing global options\n\n// ### Globals\nwindow.Parsley = window.psly = Parsley;\nwindow.ParsleyUtils = ParsleyUtils;\n\n// ### Define methods that forward to the registry, and deprecate all access except through window.Parsley\nvar registry = window.Parsley._validatorRegistry = new ParsleyValidatorRegistry(window.ParsleyConfig.validators, window.ParsleyConfig.i18n);\nwindow.ParsleyValidator = {};\n$.each('setLocale addCatalog addMessage addMessages getErrorMessage formatMessage addValidator updateValidator removeValidator'.split(' '), function (i, method) {\n  window.Parsley[method] = $.proxy(registry, method);\n  window.ParsleyValidator[method] = function () {\n    ParsleyUtils.warnOnce(`Accessing the method '${method}' through ParsleyValidator is deprecated. Simply call 'window.Parsley.${method}(...)'`);\n    return window.Parsley[method](...arguments);\n  };\n});\n\n// ### ParsleyUI\n// Deprecated global object\nwindow.Parsley.UI = ParsleyUI;\nwindow.ParsleyUI = {\n  removeError: function (instance, name, doNotUpdateClass) {\n    var updateClass = true !== doNotUpdateClass;\n    ParsleyUtils.warnOnce(`Accessing ParsleyUI is deprecated. Call 'removeError' on the instance directly. Please comment in issue 1073 as to your need to call this method.`);\n    return instance.removeError(name, {updateClass});\n  },\n  getErrorsMessages: function (instance) {\n    ParsleyUtils.warnOnce(`Accessing ParsleyUI is deprecated. Call 'getErrorsMessages' on the instance directly.`);\n    return instance.getErrorsMessages();\n  }\n};\n$.each('addError updateError'.split(' '), function (i, method) {\n  window.ParsleyUI[method] = function (instance, name, message, assert, doNotUpdateClass) {\n    var updateClass = true !== doNotUpdateClass;\n    ParsleyUtils.warnOnce(`Accessing ParsleyUI is deprecated. Call '${method}' on the instance directly. Please comment in issue 1073 as to your need to call this method.`);\n    return instance[method](name, {message, assert, updateClass});\n  };\n});\n\n// Alleviate glaring Firefox bug https://bugzilla.mozilla.org/show_bug.cgi?id=1250521\n// See also https://github.com/guillaumepotier/Parsley.js/issues/1068\nif (/firefox/i.test(navigator.userAgent)) {\n  $(document).on('change', 'select', evt => {\n    $(evt.target).trigger('input');\n  });\n}\n\n// ### PARSLEY auto-binding\n// Prevent it by setting `ParsleyConfig.autoBind` to `false`\nif (false !== window.ParsleyConfig.autoBind) {\n  $(function () {\n    // Works only on `data-parsley-validate`.\n    if ($('[data-parsley-validate]').length)\n      $('[data-parsley-validate]').parsley();\n  });\n}\n\nexport default Parsley;\n", "import $ from 'jquery';\n\nimport <PERSON><PERSON><PERSON> from './main';\n\n$.extend(true, <PERSON><PERSON><PERSON>, {\n  asyncValidators: {\n    'default': {\n      fn: function (xhr) {\n        // By default, only status 2xx are deemed successful.\n        // Note: we use status instead of state() because responses with status 200\n        // but invalid messages (e.g. an empty body for content type set to JSON) will\n        // result in state() === 'rejected'.\n        return xhr.status >= 200 && xhr.status < 300;\n      },\n      url: false\n    },\n    reverse: {\n      fn: function (xhr) {\n        // If reverse option is set, a failing ajax request is considered successful\n        return xhr.status < 200 || xhr.status >= 300;\n      },\n      url: false\n    }\n  },\n\n  addAsyncValidator: function (name, fn, url, options) {\n    Parsley.asyncValidators[name] = {\n      fn: fn,\n      url: url || false,\n      options: options || {}\n    };\n\n    return this;\n  }\n\n});\n\nParsley.addValidator('remote', {\n  requirementType: {\n    '': 'string',\n    'validator': 'string',\n    'reverse': 'boolean',\n    'options': 'object'\n  },\n\n  validateString: function (value, url, options, instance) {\n    var data = {};\n    var ajaxOptions;\n    var csr;\n    var validator = options.validator || (true === options.reverse ? 'reverse' : 'default');\n\n    if ('undefined' === typeof Parsley.asyncValidators[validator])\n      throw new Error('Calling an undefined async validator: `' + validator + '`');\n\n    url = Parsley.asyncValidators[validator].url || url;\n\n    // Fill current value\n    if (url.indexOf('{value}') > -1) {\n      url = url.replace('{value}', encodeURIComponent(value));\n    } else {\n      data[instance.$element.attr('name') || instance.$element.attr('id')] = value;\n    }\n\n    // Merge options passed in from the function with the ones in the attribute\n    var remoteOptions = $.extend(true, options.options || {} , Parsley.asyncValidators[validator].options);\n\n    // All `$.ajax(options)` could be overridden or extended directly from DOM in `data-parsley-remote-options`\n    ajaxOptions = $.extend(true, {}, {\n      url: url,\n      data: data,\n      type: 'GET'\n    }, remoteOptions);\n\n    // Generate store key based on ajax options\n    instance.trigger('field:ajaxoptions', instance, ajaxOptions);\n\n    csr = $.param(ajaxOptions);\n\n    // Initialise querry cache\n    if ('undefined' === typeof Parsley._remoteCache)\n      Parsley._remoteCache = {};\n\n    // Try to retrieve stored xhr\n    var xhr = Parsley._remoteCache[csr] = Parsley._remoteCache[csr] || $.ajax(ajaxOptions);\n\n    var handleXhr = function () {\n      var result = Parsley.asyncValidators[validator].fn.call(instance, xhr, url, options);\n      if (!result) // Map falsy results to rejected promise\n        result = $.Deferred().reject();\n      return $.when(result);\n    };\n\n    return xhr.then(handleXhr, handleXhr);\n  },\n\n  priority: -1\n});\n\nParsley.on('form:submit', function () {\n  Parsley._remoteCache = {};\n});\n\nwindow.ParsleyExtend.addAsyncValidator = function () {\n  ParsleyUtils.warnOnce('Accessing the method `addAsyncValidator` through an instance is deprecated. Simply call `Parsley.addAsyncValidator(...)`');\n  return Parsley.addAsyncValidator(...arguments);\n};\n", "// This is included with the Parsley library itself,\n// thus there is no use in adding it to your project.\nimport <PERSON><PERSON>ley from '../parsley/main';\n\nParsley.addMessages('en', {\n  defaultMessage: \"This value seems to be invalid.\",\n  type: {\n    email:        \"This value should be a valid email.\",\n    url:          \"This value should be a valid url.\",\n    number:       \"This value should be a valid number.\",\n    integer:      \"This value should be a valid integer.\",\n    digits:       \"This value should be digits.\",\n    alphanum:     \"This value should be alphanumeric.\"\n  },\n  notblank:       \"This value should not be blank.\",\n  required:       \"This value is required.\",\n  pattern:        \"This value seems to be invalid.\",\n  min:            \"This value should be greater than or equal to %s.\",\n  max:            \"This value should be lower than or equal to %s.\",\n  range:          \"This value should be between %s and %s.\",\n  minlength:      \"This value is too short. It should have %s characters or more.\",\n  maxlength:      \"This value is too long. It should have %s characters or fewer.\",\n  length:         \"This value length is invalid. It should be between %s and %s characters long.\",\n  mincheck:       \"You must select at least %s choices.\",\n  maxcheck:       \"You must select %s choices or fewer.\",\n  check:          \"You must select between %s and %s choices.\",\n  equalto:        \"This value should be the same.\"\n});\n\nParsley.setLocale('en');\n", "import $ from 'jquery';\nimport Parsley from './parsley/main';\nimport './parsley/pubsub';\nimport './parsley/remote';\nimport './i18n/en';\n\nexport default Parsley;\n"], "sourceRoot": "/source/"}