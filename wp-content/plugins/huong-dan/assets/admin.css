/* Admin styles for Huong Dan plugin */

.drag-instructions {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 15px 20px;
    margin: 20px 0;
    color: #0073aa;
    font-size: 14px;
    line-height: 1.5;
}

.drag-instructions strong {
    color: #005177;
}

.menu-manager-container {
    width: 100%;
    max-width: 1200px;
    margin-top: 20px;
    padding: 0 20px;
}

.menu-list {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 0;
    min-height: 100px;
}

.menu-item {
    border-bottom: 1px solid #f0f0f1;
    padding: 20px 25px;
    cursor: move;
    position: relative;
    background: #fff;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    min-height: 60px;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:hover {
    background-color: #f8f9fa;
}

.menu-item.parent-item {
    background: #f8f9fa;
    font-weight: 600;
    color: #1d2327;
    cursor: move;
    border-left: 4px solid #007cba;
}

.menu-item.parent-item:hover {
    background-color: #e9ecef;
}

.menu-item.child-item {
    padding-left: 40px;
    background: #fff;
    border-left: 2px solid #e0e0e0;
    margin-left: 20px;
}

.menu-item.child-item:hover {
    border-left-color: #007cba;
}

.menu-item.ui-sortable-helper {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: 1px solid #007cba;
    transform: rotate(1deg);
    z-index: 1000;
    opacity: 0.9;
}

.menu-item.ui-sortable-placeholder {
    background: #e1f5fe !important;
    border: 2px dashed #007cba !important;
    height: 60px !important;
    opacity: 0.7 !important;
    visibility: visible !important;
}

.ui-sortable-placeholder {
    background: #e1f5fe !important;
    border: 2px dashed #007cba !important;
    height: 60px !important;
    opacity: 0.7 !important;
    visibility: visible !important;
}

.child-placeholder {
    margin-left: 40px !important;
    background: #e3f2fd !important;
    border-left: 4px solid #2196f3 !important;
}

.menu-item-content {
    flex: 1;
}

.menu-item-title {
    font-size: 15px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.menu-item-title i {
    width: 18px;
    text-align: center;
    color: #007cba;
    font-size: 16px;
}

.menu-item-info {
    font-size: 13px;
    color: #646970;
    margin-top: 6px;
}

.menu-item-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.menu-item-actions a {
    text-decoration: none;
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.menu-item-actions .button-small {
    font-weight: 500;
}

.menu-item-actions .button-small:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.menu-actions {
    margin-top: 25px;
    padding: 20px 25px;
    background: #f8f9fa;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 20px;
    border: 1px solid #e0e0e0;
}

.save-status {
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.save-status.success {
    color: #00a32a;
    background: #e8f5e8;
}

.save-status.error {
    color: #d63638;
    background: #ffeaea;
}

/* Drag handle indicator */
.menu-item::before {
    content: "⋮⋮";
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 14px;
    line-height: 1;
    letter-spacing: -2px;
    font-weight: bold;
    cursor: move;
    z-index: 10;
}

.menu-item:hover::before {
    color: #007cba;
}

.menu-item.parent-item::before {
    left: 9px;
}

.menu-item.child-item::before {
    left: 25px;
}

/* Sortable states */
.ui-sortable-helper::before {
    color: #007cba;
}

/* Empty state */
.menu-list:empty::after {
    content: "Chưa có mục menu nào. Hãy thêm trang mới để tạo menu.";
    display: block;
    text-align: center;
    padding: 40px 20px;
    color: #646970;
    font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
    .menu-manager-container {
        padding: 0 10px;
    }

    .menu-item {
        padding: 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        min-height: auto;
    }

    .menu-item-title {
        font-size: 14px;
        gap: 8px;
    }

    .menu-item-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .menu-item.child-item {
        padding-left: 35px;
        margin-left: 15px;
    }

    .menu-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 20px;
    }
}

/* Animation for successful save */
@keyframes saveSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.save-status.success {
    animation: saveSuccess 0.3s ease;
}

/* Loading state */
.menu-item.loading {
    opacity: 0.6;
    pointer-events: none;
}

.menu-item.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Menu item type indicators */
.menu-item-type {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
    font-weight: 500;
}

.menu-item-type.parent {
    background: #007cba;
    color: white;
}

.menu-item-type.child {
    background: #00a32a;
    color: white;
}

.menu-item-type.no-link {
    background: #ddd;
    color: #666;
}

/* Improved visual hierarchy */
.menu-item.parent-item {
    font-size: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.menu-item.child-item {
    font-size: 14px;
    position: relative;
}

.menu-item.child-item::after {
    content: "";
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 1px;
    background: #ddd;
}

/* Focus states for accessibility */
.menu-item:focus {
    outline: 2px solid #007cba;
    outline-offset: -2px;
}

.menu-item:focus-visible {
    outline: 2px solid #007cba;
    outline-offset: -2px;
}

/* Sortable specific styles */
.ui-sortable {
    min-height: 50px;
}

.ui-sortable .menu-item {
    margin: 0;
    position: relative;
}

.ui-sortable-helper {
    box-shadow: 0 5px 15px rgba(0,0,0,0.3) !important;
    transform: rotate(2deg) !important;
    z-index: 1000 !important;
    border: 2px solid #007cba !important;
}

.ui-sortable-placeholder {
    border: 2px dashed #007cba !important;
    background: #e3f2fd !important;
    height: 60px !important;
    margin: 2px 0 !important;
    border-radius: 4px !important;
}

/* Drag instructions */
.drag-instructions {
    background: #f0f6fc;
    border: 1px solid #c3d9ed;
    border-radius: 4px;
    padding: 10px 15px;
    margin-bottom: 15px;
    color: #0073aa;
    font-size: 13px;
}

.drag-instructions::before {
    content: "💡 ";
    margin-right: 5px;
}
