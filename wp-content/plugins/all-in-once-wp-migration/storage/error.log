Mar 06 2019 07:03:08
{"type":2,"message":"unlink(\/home\/<USER>\/domains\/itsweb.vn\/public_html\/wp-content\/wflogs\/template.0464028001551855787.tmp): No such file or directory","file":"\/home\/<USER>\/domains\/itsweb.vn\/public_html\/wp-content\/plugins\/wordfence\/lib\/wordfenceClass.php","line":1871}

Apr 05 2019 08:27:06
{"type":2,"message":"is_readable(): open_basedir restriction in effect. File(\/dev\/urandom) is not within the allowed path(s): (\/home\/<USER>\/:\/tmp:\/var\/tmp:\/usr\/local\/lib\/php\/:\/usr\/local\/php71\/lib\/php\/)","file":"\/home\/<USER>\/domains\/itsweb.vn\/public_html\/wp-includes\/class-phpass.php","line":68}

Aug 06 2024 03:31:23
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:23
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:23
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:23
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:23
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:24
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:24
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:24
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:24
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:25
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:25
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:25
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:25
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:26
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:26
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:26
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:26
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:26
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:27
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:27
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:28
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:28
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:30
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:30
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:31
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:31
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:31
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:31
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:32
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:32
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:32
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:32
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:32
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:33
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:33
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:33
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:33
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:34
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:34
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:34
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:34
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:35
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:35
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:35
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:36
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:36
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:36
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:36
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:36
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:37
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:37
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:37
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:37
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:38
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:38
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:38
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:38
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:38
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:39
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:39
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:39
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:39
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:40
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:40
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:40
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:40
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:40
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:41
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:57
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:31:58
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:32:08
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:32:18
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Aug 06 2024 03:32:29
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\cetech\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:24
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:24
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:25
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:25
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:26
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:26
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:27
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:27
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:28
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:28
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:29
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:31
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:35
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:35
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:36
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:37
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:38
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:38
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:39
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:40
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:41
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:42
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:43
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:44
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:44
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:45
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:46
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:46
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:47
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:48
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:52
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:21:53
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:22:04
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:22:10
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:22:11
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:11
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 04:22:12
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:22:13
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 04:22:13
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 04:38:10
{"Number":8192,"Message":"explode(): Passing null to parameter #2 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\functions.php","Line":338}

Feb 11 2025 04:38:10
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 04:38:11
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 04:38:11
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 04:38:11
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 04:38:12
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 04:38:17
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 04:38:17
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 04:38:18
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 04:38:18
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 04:38:19
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:55
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:55
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:56
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:56
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:56
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:57
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:57
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:58
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:58
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:59
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:59
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:42:59
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:00
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:00
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:01
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:01
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:02
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:02
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:03
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:03
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:03
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:04
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:04
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:05
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:05
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:06
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:06
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:06
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:07
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:07
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:08
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:08
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:10
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:10
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:11
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:13
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:13
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:14
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:14
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:14
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:15
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:15
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:16
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:16
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:16
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:17
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:17
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:18
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:18
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:19
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:19
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:19
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:20
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:20
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:21
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:21
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:22
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:22
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:58
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:43:59
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:44:09
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:44:12
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:44:14
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:14
{"Number":8192,"Message":"rtrim(): Passing null to parameter #1 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-includes\\formatting.php","Line":2819}

Feb 11 2025 06:44:16
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 06:44:16
{"type":8192,"message":"Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\lib\\vendor\\servmask\\filter\\class-ai1wm-recursive-newline-filter.php","line":28}

Feb 11 2025 06:44:16
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 07:19:34
{"Number":8192,"Message":"explode(): Passing null to parameter #2 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\functions.php","Line":338}

Feb 11 2025 07:19:34
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 07:19:34
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 07:19:35
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 07:19:35
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 07:19:36
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 07:19:37
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 07:19:38
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 11 2025 07:19:38
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 13 2025 05:11:51
{"Number":8192,"Message":"explode(): Passing null to parameter #2 ($string) of type string is deprecated","File":"D:\\laragon\\www\\battu\\wp-content\\plugins\\all-in-once-wp-migration\\functions.php","Line":338}

Feb 13 2025 05:11:51
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 13 2025 05:11:51
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 13 2025 05:11:52
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 13 2025 05:11:52
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 13 2025 05:11:53
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 13 2025 05:11:58
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 13 2025 05:11:59
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 13 2025 05:11:59
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 13 2025 05:12:00
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

Feb 13 2025 05:12:00
{"type":2,"message":"Attempt to read property \"has_archive\" on null","file":"D:\\laragon\\www\\battu\\wp-content\\plugins\\seo-by-rank-math\\includes\\settings\\titles\\post-types.php","line":391}

